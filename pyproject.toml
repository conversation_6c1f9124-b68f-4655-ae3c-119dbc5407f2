[tool.poetry]
name = "hospital-equipment-maintenance"
version = "0.1.0"
description = "Hospital Equipment Maintenance Management and Reminder System"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
chardet = "^5.0.0"
bcrypt = "^4.0.1"
python = "^3.11"
flask = "^2.3.3"
pydantic = "^2.5.0"
pandas = "^2.1.0"
schedule = "^1.2.0"
python-dotenv = "^1.0.0"
apscheduler = "^3.10.4"  # For scheduling background tasks
email-validator = "^2.1.0"
flask-wtf = "^1.2.1"  # For form validation
google-auth = "^2.40.3"
google-auth-oauthlib = "^1.2.2"
google-api-python-client = "^2.172.0"
mailjet-rest = "^1.4.0"
gunicorn = "^23.0.0"
pywebpush = "^2.0.3"
python-barcode = "^0.15.1"
Pillow = "^10.0.0"
flask-session = "^0.8.0"
flask-login = "^0.6.3"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"

[tool.poetry.scripts]
start-app = "app.main:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 88
