[{"id": 1, "timestamp": "2025-07-31 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 17 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 17, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 2, "timestamp": "2025-07-31 07:29:03", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 17 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 17, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 3, "timestamp": "2025-07-31 07:29:04", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 17 equipment maintenance tasks (15-day threshold, MEDIUM priority)", "status": "Success", "details": {"threshold_days": 15, "priority_level": "MEDIUM", "equipment_count": 17, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 4, "timestamp": "2025-07-31 07:29:04", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 5, "timestamp": "2025-07-31 07:29:05", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}, {"id": 6, "timestamp": "2025-07-31 07:29:06", "event_type": "<PERSON><PERSON><PERSON>", "performed_by": "System", "description": "Email reminder sent for 30 equipment maintenance tasks (30-day threshold, LOW priority)", "status": "Success", "details": {"threshold_days": 30, "priority_level": "LOW", "equipment_count": 30, "recipient": "<EMAIL>", "email_type": "threshold_reminder"}}]