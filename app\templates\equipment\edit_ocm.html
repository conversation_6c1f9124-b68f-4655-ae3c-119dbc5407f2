{% extends 'base.html' %}

{% block title %}Edit OCM Equipment{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2 class="mb-4">Edit OCM Equipment ({{ entry.Serial }})</h2>
    <form action="{{ url_for('views.edit_ocm_equipment', Serial=entry.Serial) }}" method="post">
        <div class="row">
            <div class="col-md-6 mb-3">
                <label for="Department" class="form-label">Department <span class="text-danger">*</span></label>
                <select class="form-select" id="Department" name="Department" required>
                    <option value="">Select Department</option>
                    {% for dept in departments %}
                        <option value="{{ dept }}" {% if (request.form.Department if request.form else entry.Department) == dept %}selected{% endif %}>{{ dept }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-6 mb-3">
                <label for="Name" class="form-label">Equipment Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="Name" name="Name" value="{{ request.form.Name if request.form else entry.Name }}" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="Model" class="form-label">Model <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="Model" name="Model" value="{{ request.form.Model if request.form else entry.Model }}" required>
            </div>
            <div class="col-md-4 mb-3">
                <label for="Serial" class="form-label">Serial <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="Serial" name="Serial" value="{{ entry.Serial }}" readonly required>
            </div>
            <div class="col-md-4 mb-3">
                <label for="Manufacturer" class="form-label">Manufacturer <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="Manufacturer" name="Manufacturer" value="{{ request.form.Manufacturer if request.form else entry.Manufacturer }}" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="Log_Number" class="form-label">Log Number <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="Log_Number" name="Log_Number" value="{{ request.form['Log_Number'] if request.form else entry['Log_Number'] }}" required>
            </div>
            <div class="col-md-4 mb-3">
                <label for="Installation_Date" class="form-label">
                    <i class="fas fa-calendar-alt text-primary me-2"></i>Installation Date
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-calendar"></i>
                    </span>
                    <input type="text" class="form-control modern-date-picker" id="Installation_Date" name="Installation_Date"
                           value="{{ request.form['Installation_Date'] if request.form else entry['Installation_Date'] }}"
                           placeholder="dd/mm/yyyy (optional)"
                           pattern="^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\d{4}$"
                           title="Please enter date in dd/mm/yyyy format">
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <label for="Warranty_End" class="form-label">
                    <i class="fas fa-shield-alt text-warning me-2"></i>Warranty End
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-calendar-times"></i>
                    </span>
                    <input type="text" class="form-control modern-date-picker" id="Warranty_End" name="Warranty_End"
                           value="{{ request.form['Warranty_End'] if request.form else entry['Warranty_End'] }}"
                           placeholder="dd/mm/yyyy (optional)"
                           pattern="^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\d{4}$"
                           title="Please enter date in dd/mm/yyyy format">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="Service_Date" class="form-label">
                    <i class="fas fa-tools text-success me-2"></i>Service Date <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-calendar-check"></i>
                    </span>
                    <input type="text" class="form-control modern-date-picker" id="Service_Date" name="Service_Date" 
                           value="{{ request.form['Service_Date'] if request.form else entry['Service_Date'] }}"
                           placeholder="dd/mm/yyyy" 
                           pattern="^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\d{4}$"
                           title="Please enter date in dd/mm/yyyy format" required>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <label for="Next_Maintenance" class="form-label">
                    <i class="fas fa-calendar-plus text-info me-2"></i>Next Maintenance <span class="text-danger">*</span>
                </label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-calendar-week"></i>
                    </span>
                    <input type="text" class="form-control modern-date-picker" id="Next_Maintenance" name="Next_Maintenance" 
                           value="{{ request.form['Next_Maintenance'] if request.form else entry['Next_Maintenance'] }}"
                           placeholder="dd/mm/yyyy" 
                           pattern="^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/\d{4}$"
                           title="Please enter date in dd/mm/yyyy format" required>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <label for="Engineer" class="form-label">Engineer <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="Engineer" name="Engineer" value="{{ request.form.Engineer if request.form else entry.Engineer }}" required>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="Status" class="form-label">Status <span class="text-danger">*</span></label>
                <select class="form-select" id="Status" name="Status" required>
                    <option value="">Select Status</option>
                    <option value="Upcoming" {% if (request.form.Status if request.form else entry.Status) == 'Upcoming' %}selected{% endif %}>Upcoming</option>
                    <option value="Overdue" {% if (request.form.Status if request.form else entry.Status) == 'Overdue' %}selected{% endif %}>Overdue</option>
                    <option value="Maintained" {% if (request.form.Status if request.form else entry.Status) == 'Maintained' %}selected{% endif %}>Maintained</option>
                </select>
            </div>
        </div>

        <div class="d-flex gap-2 mt-3">
            <button type="submit" class="btn btn-primary">Update OCM Equipment</button>
            <a href="{{ url_for('views.equipment_history', equipment_type='ocm', equipment_id=entry.Serial) }}"
               class="btn btn-info">
                <i class="fas fa-history"></i> View History
            </a>
            <a href="{{ url_for('views.add_equipment_history', equipment_type='ocm', equipment_id=entry.Serial) }}"
               class="btn btn-success">
                <i class="fas fa-plus"></i> Add History
            </a>
            <a href="{{ url_for('views.list_equipment', data_type='ocm') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Register department dropdown for real-time synchronization
    const departmentSelect = document.getElementById('Department');
    if (departmentSelect && window.registerDepartmentDropdown) {
        window.registerDepartmentDropdown(departmentSelect, {
            defaultOption: '<option value="">Select Department</option>',
            useId: false // Use department name as value
        });
        console.log('OCM Edit Page: Department dropdown registered for synchronization');
    }
});
</script>
{% endblock %}
