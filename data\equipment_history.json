[{"id": "ab0b75f1-74da-465c-8788-8e715cc7e708", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "1-The ENT Workstation is having a medical failure .(low or weak water pressure)", "created_at": "2025-06-29 22:47:46", "updated_at": "2025-06-29 22:47:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a2641a88-38bc-4e75-b7aa-2ae5b5d54554", "note_id": "ab0b75f1-74da-465c-8788-8e715cc7e708", "original_filename": "incident_related_medical_device.pdf", "stored_filename": "6b46183f-1651-4bdc-a458-b87e66c017ef.pdf", "file_path": "app/static/uploads/history\\6b46183f-1651-4bdc-a458-b87e66c017ef.pdf", "mime_type": "application/pdf", "file_size": 2708815, "upload_date": "2025-06-29 22:47:46"}]}, {"id": "f12423f1-9294-4190-baab-6aea86ddb2c5", "equipment_id": "34234", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created_at": "2025-06-29 23:27:39", "updated_at": "2025-06-29 23:27:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9c2cad76-ed70-4e7f-a249-5f78c0db8a6f", "note_id": "f12423f1-9294-4190-baab-6aea86ddb2c5", "original_filename": "incident_related_medical_device.pdf", "stored_filename": "a256b7a0-92f3-476d-97ee-ee111f8f75f1.pdf", "file_path": "app/static/uploads/history\\a256b7a0-92f3-476d-97ee-ee111f8f75f1.pdf", "mime_type": "application/pdf", "file_size": 2708815, "upload_date": "2025-06-29 23:27:39"}]}, {"id": "c13894ad-05a3-4266-a33c-ff061cb1b0d1", "equipment_id": "380-2008-44-196", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Technical issue identified. \r\nMicroswitch was found defective and replaced. \r\nThe bed is now functioning properly.", "created_at": "2025-07-19 14:18:44", "updated_at": "2025-07-19 14:18:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0f40e9f1-cea0-4275-b9b1-34fcf09c7a01", "note_id": "c13894ad-05a3-4266-a33c-ff061cb1b0d1", "original_filename": "380-2008-44-196.pdf", "stored_filename": "9fa30713-5f92-41c8-9bcd-92a770ff24be.pdf", "file_path": "app/static/uploads/history/9fa30713-5f92-41c8-9bcd-92a770ff24be.pdf", "mime_type": "application/pdf", "file_size": 1833839, "upload_date": "2025-07-19 14:18:44"}]}, {"id": "875d4f1a-ac90-4e2d-aa76-e64b1b4c3d53", "equipment_id": "80091176", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified faulty ECG cable (C2 lead not detecting).\r\nCable replaced.\r\nECG machine tested and confirmed to be functioning properly.", "created_at": "2025-07-19 15:31:46", "updated_at": "2025-07-19 15:31:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7f54f5c0-a593-4a52-88a5-465e37de6b1e", "note_id": "875d4f1a-ac90-4e2d-aa76-e64b1b4c3d53", "original_filename": "80091176.pdf", "stored_filename": "e3eb5343-2a28-45ba-9a44-a0f9477d4987.pdf", "file_path": "app/static/uploads/history/e3eb5343-2a28-45ba-9a44-a0f9477d4987.pdf", "mime_type": "application/pdf", "file_size": 774478, "upload_date": "2025-07-19 15:31:46"}]}, {"id": "240f53fe-41aa-4b07-afaf-37dfb1bdc60c", "equipment_id": "P770V14040003", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected and tested pressure.\r\nBoth ports are functioning properly. \r\nPower socket was also checked.-no issues found.\r\nUnit is working normally.", "created_at": "2025-07-19 15:37:59", "updated_at": "2025-07-19 15:37:59", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fdbe882b-5fe4-4e01-b850-778a921e9c32", "note_id": "240f53fe-41aa-4b07-afaf-37dfb1bdc60c", "original_filename": "P770B1404003.pdf", "stored_filename": "03a8f65d-8ab8-4304-b636-3822fac0e8fb.pdf", "file_path": "app/static/uploads/history/03a8f65d-8ab8-4304-b636-3822fac0e8fb.pdf", "mime_type": "application/pdf", "file_size": 794910, "upload_date": "2025-07-19 15:37:59"}]}, {"id": "f6ddb237-4788-42c7-9ad0-1a3b7296e2a5", "equipment_id": "D770B20120024", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Touch panel was found to be non-functional and was replaced.\r\nUnit has been tested and now operating normally", "created_at": "2025-07-19 15:43:08", "updated_at": "2025-07-19 15:43:08", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8dc5ef20-ea00-4996-a8ef-3fc87ad91666", "note_id": "f6ddb237-4788-42c7-9ad0-1a3b7296e2a5", "original_filename": "D770B20120024.pdf", "stored_filename": "8b1b58c3-d533-490a-a08f-916ca3f706de.pdf", "file_path": "app/static/uploads/history/8b1b58c3-d533-490a-a08f-916ca3f706de.pdf", "mime_type": "application/pdf", "file_size": 775278, "upload_date": "2025-07-19 15:43:08"}]}, {"id": "857dda4f-70ef-41d6-93b9-742ab791dcf7", "equipment_id": "D770B14040005", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified damaged limb therapy cuff connector and replaced it.\r\nUnit was tested and is functioning properly.", "created_at": "2025-07-19 15:47:29", "updated_at": "2025-07-19 15:47:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "14f75717-f1ae-4634-87a8-b60a929a2372", "note_id": "857dda4f-70ef-41d6-93b9-742ab791dcf7", "original_filename": "D770B14040005.pdf", "stored_filename": "5a375b2c-eaa4-4478-b344-49d566610e92.pdf", "file_path": "app/static/uploads/history/5a375b2c-eaa4-4478-b344-49d566610e92.pdf", "mime_type": "application/pdf", "file_size": 762526, "upload_date": "2025-07-19 15:47:29"}]}, {"id": "c50a6015-b6fe-47ef-b22b-c29ca01067a9", "equipment_id": "P770814040004", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected and tested the cuff. Connected it to the unit and confirmed proper inflation. \r\n<PERSON><PERSON> is functioning normally and no issue were found. \r\nUnit is working properly.", "created_at": "2025-07-19 15:53:12", "updated_at": "2025-07-19 15:53:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5f22eac1-3890-417d-9468-298d3867238a", "note_id": "c50a6015-b6fe-47ef-b22b-c29ca01067a9", "original_filename": "P770B140004.pdf", "stored_filename": "42836230-5771-4787-89b7-b4b00258217f.pdf", "file_path": "app/static/uploads/history/42836230-5771-4787-89b7-b4b00258217f.pdf", "mime_type": "application/pdf", "file_size": 751806, "upload_date": "2025-07-19 15:53:13"}]}, {"id": "1cbb95da-d2ae-4c6d-bb6d-1e78ffdb5458", "equipment_id": "29538", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Performed scheduled preventive maintenance.\r\nUnit was calibrated and confirmed to be functioning properly.", "created_at": "2025-07-19 15:59:39", "updated_at": "2025-07-19 15:59:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9e7adc99-5144-4d6e-8a93-62ada20d4395", "note_id": "1cbb95da-d2ae-4c6d-bb6d-1e78ffdb5458", "original_filename": "29538.pdf", "stored_filename": "46bcb170-d3c1-4f41-8a75-8f8dfe1a7ded.pdf", "file_path": "app/static/uploads/history/46bcb170-d3c1-4f41-8a75-8f8dfe1a7ded.pdf", "mime_type": "application/pdf", "file_size": 360144, "upload_date": "2025-07-19 15:59:39"}]}, {"id": "1b49ead5-f3a0-437f-9c6f-271f9ee70865", "equipment_id": "72210006", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Performed Baseline Check on the unit.", "created_at": "2025-07-19 16:41:07", "updated_at": "2025-07-19 16:41:07", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a6946db1-5891-492c-b9bc-78cbde537be9", "note_id": "1b49ead5-f3a0-437f-9c6f-271f9ee70865", "original_filename": "72210006.pdf", "stored_filename": "c1f741b8-7dd2-4f76-876c-44c82684320e.pdf", "file_path": "app/static/uploads/history/c1f741b8-7dd2-4f76-876c-44c82684320e.pdf", "mime_type": "application/pdf", "file_size": 1035791, "upload_date": "2025-07-19 16:41:07"}]}, {"id": "f5521445-ce96-4cc8-b7a6-2ebe16097e59", "equipment_id": "118257", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Initial issue reported: No laser output. \r\nRe-installed the system completely and perform full installation procedures.\r\nAll connection were tested.\r\nSystem is now functioning properly.", "created_at": "2025-07-19 16:46:33", "updated_at": "2025-07-19 16:46:33", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7818483c-3d9f-40a6-8883-9599428b1477", "note_id": "f5521445-ce96-4cc8-b7a6-2ebe16097e59", "original_filename": "118257.pdf", "stored_filename": "c2fb9aa9-51f6-4a28-81e6-c8513b793f50.pdf", "file_path": "app/static/uploads/history/c2fb9aa9-51f6-4a28-81e6-c8513b793f50.pdf", "mime_type": "application/pdf", "file_size": 1011359, "upload_date": "2025-07-19 16:46:33"}]}, {"id": "f891bbd0-d2dd-4eb3-b062-2ffb20d2c9ee", "equipment_id": "72210006", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified that the compressed air connection was removed. \r\nReplaced with a new connection, checked, and confirmed that the compressed air system is now functioning properly.", "created_at": "2025-07-19 17:16:35", "updated_at": "2025-07-19 17:16:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "95906867-fe0b-4dfd-983c-4899a8f4aae6", "note_id": "f891bbd0-d2dd-4eb3-b062-2ffb20d2c9ee", "original_filename": "72210006_a.pdf", "stored_filename": "9afb9a9e-6eed-42f8-a669-e92fc8d458b9.pdf", "file_path": "app/static/uploads/history/9afb9a9e-6eed-42f8-a669-e92fc8d458b9.pdf", "mime_type": "application/pdf", "file_size": 790142, "upload_date": "2025-07-19 17:16:35"}]}, {"id": "5e2fa2ff-44d8-43f7-a5d2-f0ed7194bebf", "equipment_id": "CE002", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Performed scheduled preventive maintenance.\r\nUnit was calibrated.\r\nConfirmed to be functioning properly.", "created_at": "2025-07-19 17:25:39", "updated_at": "2025-07-19 17:25:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "72b9abce-a19d-45d0-8e1e-a03552d942f4", "note_id": "5e2fa2ff-44d8-43f7-a5d2-f0ed7194bebf", "original_filename": "CE002.pdf", "stored_filename": "982f4f76-7f0e-45be-abf8-3876004a1bcc.pdf", "file_path": "app/static/uploads/history/982f4f76-7f0e-45be-abf8-3876004a1bcc.pdf", "mime_type": "application/pdf", "file_size": 812719, "upload_date": "2025-07-19 17:25:39"}]}, {"id": "fc04d885-f7b2-4ce6-9190-762cee5d270d", "equipment_id": "027390-19770208", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "identified a power supply issue.\r\nrepaired the power supply module\r\nunit confirmed to be functioning properly", "created_at": "2025-07-19 17:47:59", "updated_at": "2025-07-19 17:47:59", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b1244763-1ff2-412d-8274-2a47fd50d902", "note_id": "fc04d885-f7b2-4ce6-9190-762cee5d270d", "original_filename": "19770208.pdf", "stored_filename": "b6609ba0-f50f-4fff-98a2-d4abb6e45367.pdf", "file_path": "app/static/uploads/history/b6609ba0-f50f-4fff-98a2-d4abb6e45367.pdf", "mime_type": "application/pdf", "file_size": 679278, "upload_date": "2025-07-19 17:47:59"}]}, {"id": "ac1c1c80-c4d3-4172-b927-8313f1f9c13b", "equipment_id": "19357626", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "After evaluation, the unit found a continuous alarm error.\r\nThe unit was taken to the company for repair.", "created_at": "2025-07-19 17:54:16", "updated_at": "2025-07-19 17:54:16", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7d00727d-9ee0-45dd-b71d-214bbbfbc2ae", "note_id": "ac1c1c80-c4d3-4172-b927-8313f1f9c13b", "original_filename": "19357626.pdf", "stored_filename": "d34fb7ed-b1a9-48bf-880a-0071b482ba3c.pdf", "file_path": "app/static/uploads/history/d34fb7ed-b1a9-48bf-880a-0071b482ba3c.pdf", "mime_type": "application/pdf", "file_size": 688654, "upload_date": "2025-07-19 17:54:16"}]}, {"id": "c71687c6-4a69-4e9d-8ca5-7499b592077e", "equipment_id": "27192", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "After the service call inspected the unit, they found out that the cable inside the power plug was broken.\r\nReplace it with a 3 pin pong plug.\r\nAfter checking, the unit was confirmed to be working properly.", "created_at": "2025-07-20 06:22:01", "updated_at": "2025-07-20 06:22:01", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "28fee3eb-5ae4-4830-a508-515acd1e60cb", "note_id": "c71687c6-4a69-4e9d-8ca5-7499b592077e", "original_filename": "27192.pdf", "stored_filename": "ef51872b-d66d-4557-8495-4c3055891928.pdf", "file_path": "app/static/uploads/history/ef51872b-d66d-4557-8495-4c3055891928.pdf", "mime_type": "application/pdf", "file_size": 790990, "upload_date": "2025-07-20 06:22:01"}]}, {"id": "68848410-14bc-4e9d-baa0-561eacce3ac6", "equipment_id": "027390-19770208", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "After evaluation, the unit is showing continuous alarm.\r\nThe unit needs to be taken to their office for further evaluation and repair.", "created_at": "2025-07-20 06:31:50", "updated_at": "2025-07-20 06:31:50", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f0e9e340-4d34-4c55-9afc-444a885ec665", "note_id": "68848410-14bc-4e9d-baa0-561eacce3ac6", "original_filename": "19770208_B.pdf", "stored_filename": "c84618ce-cecd-445d-a657-241b0914853a.pdf", "file_path": "app/static/uploads/history/c84618ce-cecd-445d-a657-241b0914853a.pdf", "mime_type": "application/pdf", "file_size": 739550, "upload_date": "2025-07-20 06:31:50"}]}, {"id": "5aead6f4-ce79-4bce-98a7-88d5d3b0c16f", "equipment_id": "1070054", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The service call found out that the adapter pin is faulty and the battery is in error.\r\nThe battery is taken for checking.\r\nThe adapter needs repair.\r\nReplacement for the adapter is still ongoing; updates will follow.", "created_at": "2025-07-20 06:57:48", "updated_at": "2025-07-20 06:57:48", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5d9e9aab-4a5a-4ab1-8186-decaa9aa62f8", "note_id": "5aead6f4-ce79-4bce-98a7-88d5d3b0c16f", "original_filename": "01070054.pdf", "stored_filename": "b5005571-552c-4125-8d23-6d86989ab3f3.pdf", "file_path": "app/static/uploads/history/b5005571-552c-4125-8d23-6d86989ab3f3.pdf", "mime_type": "application/pdf", "file_size": 779342, "upload_date": "2025-07-20 06:57:48"}]}, {"id": "902bf235-5ec5-455d-b2d3-1b213879912e", "equipment_id": "2251-16", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Checked the unit, and the battery is faulty; needs to change the battery.", "created_at": "2025-07-20 07:37:32", "updated_at": "2025-07-20 07:37:32", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8473f344-7963-4508-8f1b-197d8baf06c3", "note_id": "902bf235-5ec5-455d-b2d3-1b213879912e", "original_filename": "2251-16.pdf", "stored_filename": "ae3a35c8-9d68-4c11-9433-674f37abca65.pdf", "file_path": "app/static/uploads/history/ae3a35c8-9d68-4c11-9433-674f37abca65.pdf", "mime_type": "application/pdf", "file_size": 2059311, "upload_date": "2025-07-20 07:37:32"}]}, {"id": "28e1962b-3117-4a9c-bc5e-0eb79e8f68f1", "equipment_id": "T6313C", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue observed: power turned off.\r\nReplaced the voltage regulator and power plug.\r\nSystem was tested and confirmed to be functioning properly.", "created_at": "2025-07-20 07:47:30", "updated_at": "2025-07-20 07:47:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "64e97745-ea4f-477e-a8c9-51fbdfd55eed", "note_id": "28e1962b-3117-4a9c-bc5e-0eb79e8f68f1", "original_filename": "T6313C.pdf", "stored_filename": "bd430992-87fd-43b7-bc3c-867fc38c7fd9.pdf", "file_path": "app/static/uploads/history/bd430992-87fd-43b7-bc3c-867fc38c7fd9.pdf", "mime_type": "application/pdf", "file_size": 815326, "upload_date": "2025-07-20 07:47:30"}]}, {"id": "4812bab2-562f-4c04-91ef-9ebf508082b7", "equipment_id": "822897", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Digital reflector relay box has power issue.\r\nNeed to be replaced.", "created_at": "2025-07-20 07:49:31", "updated_at": "2025-07-20 07:49:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "6ae41517-56ec-4c1c-8a75-f969c06be165", "note_id": "4812bab2-562f-4c04-91ef-9ebf508082b7", "original_filename": "822897.pdf", "stored_filename": "693f1eaf-0e87-4ae3-8f8a-8e7f0a6b0e6a.pdf", "file_path": "app/static/uploads/history/693f1eaf-0e87-4ae3-8f8a-8e7f0a6b0e6a.pdf", "mime_type": "application/pdf", "file_size": 590735, "upload_date": "2025-07-20 07:49:31"}]}, {"id": "e0ad6c0e-1b7f-4c9e-b6f8-766992bf9e18", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified broken drainage line.\r\nOpened the drain passage and cleaned it.\r\nChecked and tested- no leaks detected.\r\nUnit is now functioning properly.", "created_at": "2025-07-20 08:01:06", "updated_at": "2025-07-20 08:01:06", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c633d7da-10fa-4513-848d-337f4175bc18", "note_id": "e0ad6c0e-1b7f-4c9e-b6f8-766992bf9e18", "original_filename": "111632045-_b.pdf", "stored_filename": "113139ed-2b8f-4ad8-8808-31990c8a9086.pdf", "file_path": "app/static/uploads/history/113139ed-2b8f-4ad8-8808-31990c8a9086.pdf", "mime_type": "application/pdf", "file_size": 1976559, "upload_date": "2025-07-20 08:01:06"}]}, {"id": "d4c08659-ca62-4428-bdfc-10e9edec32d7", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Observed water leakage from the handpiece. \r\nIssue was resolved. \r\nUnit was checked and is now functioning properly. \r\nPlaced under further observation for monitoring.", "created_at": "2025-07-20 08:07:50", "updated_at": "2025-07-20 08:07:50", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "45c03bec-6fd5-47ba-8e82-a028a1239a31", "note_id": "d4c08659-ca62-4428-bdfc-10e9edec32d7", "original_filename": "111632045-_c.pdf", "stored_filename": "ce3ae44f-0a1e-498b-a83b-6cbd39a75b7f.pdf", "file_path": "app/static/uploads/history/ce3ae44f-0a1e-498b-a83b-6cbd39a75b7f.pdf", "mime_type": "application/pdf", "file_size": 830494, "upload_date": "2025-07-20 08:07:50"}]}, {"id": "dc3ede5b-60eb-406d-ba60-239091e9ee2e", "equipment_id": "103732017", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "No water flow from spray handpiece. \r\nCleaned and cleared blockage and filter. \r\nUnit was tested and confirmed to be working properly.", "created_at": "2025-07-20 08:13:30", "updated_at": "2025-07-20 08:13:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "68d8c8e1-fce2-4feb-9713-9b7105aad384", "note_id": "dc3ede5b-60eb-406d-ba60-239091e9ee2e", "original_filename": "103732017.pdf", "stored_filename": "734a10bc-8219-4dfa-822a-b1beb46d5094.pdf", "file_path": "app/static/uploads/history/734a10bc-8219-4dfa-822a-b1beb46d5094.pdf", "mime_type": "application/pdf", "file_size": 511312, "upload_date": "2025-07-20 08:13:30"}]}, {"id": "add6beb1-6421-4fa6-9bc4-bfa4934f86d0", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Leakage detected from the irrigation handpiece.\r\nTread on the handpiece is damaged and requires replacement. \r\nQuotation for the replacement part will be submitted as soon as possible.", "created_at": "2025-07-20 08:30:13", "updated_at": "2025-07-20 08:30:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "cd57ad67-27c2-4622-872c-4f91a222f2e6", "note_id": "add6beb1-6421-4fa6-9bc4-bfa4934f86d0", "original_filename": "111632045-_d.pdf", "stored_filename": "08f8a3eb-de67-464d-bb0f-aa129c4c3a6d.pdf", "file_path": "app/static/uploads/history/08f8a3eb-de67-464d-bb0f-aa129c4c3a6d.pdf", "mime_type": "application/pdf", "file_size": 1968975, "upload_date": "2025-07-20 08:30:13"}]}, {"id": "21c585f5-0664-487d-9e75-82dbc48d3f9a", "equipment_id": "UN2000597", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue: No display and loose cable connection detected.\r\nReplaced the display cable from AIDA to the touchscreen.\r\nSignal successfully re-established and display is now functioning properly.", "created_at": "2025-07-20 08:35:27", "updated_at": "2025-07-20 08:35:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2e0bf9dd-cf2f-465c-935f-6221ad2e8899", "note_id": "21c585f5-0664-487d-9e75-82dbc48d3f9a", "original_filename": "UN2000597.pdf", "stored_filename": "57af2d53-3888-4ff9-8af3-426f7bc7b74b.pdf", "file_path": "app/static/uploads/history/57af2d53-3888-4ff9-8af3-426f7bc7b74b.pdf", "mime_type": "application/pdf", "file_size": 1672047, "upload_date": "2025-07-20 08:35:27"}]}, {"id": "44be284e-df50-40cd-89b8-c6d935b69ade", "equipment_id": "2632794", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "<PERSON><PERSON> (up/down) was not functioning.\r\nChecked and secured all connections.\r\nIssue resolved and full movement restored.\r\nUnit is now operating properly.", "created_at": "2025-07-20 08:40:58", "updated_at": "2025-07-20 08:40:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1d3118da-58e0-4daf-84f0-b5306632eb9a", "note_id": "44be284e-df50-40cd-89b8-c6d935b69ade", "original_filename": "2632794.pdf", "stored_filename": "3529f26e-c953-4c22-8253-5a983b06d284.pdf", "file_path": "app/static/uploads/history/3529f26e-c953-4c22-8253-5a983b06d284.pdf", "mime_type": "application/pdf", "file_size": 771902, "upload_date": "2025-07-20 08:40:58"}]}, {"id": "7b43e941-b997-4010-a359-8acad4d711a5", "equipment_id": "103732017", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Water was not heating due to low water pressure. \r\nBypassed the pressure switch, and the unit resumed normal operation. \r\nFunctionality confirmed.", "created_at": "2025-07-20 08:48:58", "updated_at": "2025-07-20 08:48:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0fd00da0-d2a3-4359-8a37-e85d0af251a4", "note_id": "7b43e941-b997-4010-a359-8acad4d711a5", "original_filename": "1073732017-_b.pdf", "stored_filename": "54b6681b-7b25-4a1f-91f5-9e6059cc55cc.pdf", "file_path": "app/static/uploads/history/54b6681b-7b25-4a1f-91f5-9e6059cc55cc.pdf", "mime_type": "application/pdf", "file_size": 1357503, "upload_date": "2025-07-20 08:48:58"}]}, {"id": "990e53d8-1ce6-4f73-93d7-0bde482a526f", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Irrigation tip was found loose from the handpiece thread.\r\nTightened securely, checked and confirmed there is no leakage. \r\nUnit is functioning properly.", "created_at": "2025-07-20 08:59:06", "updated_at": "2025-07-20 08:59:06", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ffc90e90-9286-4a81-9fc5-f889e4a269f0", "note_id": "990e53d8-1ce6-4f73-93d7-0bde482a526f", "original_filename": "111632045-_e.pdf", "stored_filename": "c4c859d1-76df-4325-84bb-5520f1cb1659.pdf", "file_path": "app/static/uploads/history/c4c859d1-76df-4325-84bb-5520f1cb1659.pdf", "mime_type": "application/pdf", "file_size": 718238, "upload_date": "2025-07-20 08:59:06"}]}, {"id": "8b925f4f-4ce4-41e9-af73-64769e8d9bd0", "equipment_id": "UN2000597", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit was inspected and found to have a loose display cable. \r\nCable was secured and the issue has been resolved. \r\nDisplay is now functioning properly.", "created_at": "2025-07-20 09:02:53", "updated_at": "2025-07-20 09:02:53", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ec0e76a2-1cba-4b31-988a-41f537f6383e", "note_id": "8b925f4f-4ce4-41e9-af73-64769e8d9bd0", "original_filename": "UN2000597-_b.pdf", "stored_filename": "fa7aab2b-0faf-4b4c-957f-9f83db4fc319.pdf", "file_path": "app/static/uploads/history/fa7aab2b-0faf-4b4c-957f-9f83db4fc319.pdf", "mime_type": "application/pdf", "file_size": 737822, "upload_date": "2025-07-20 09:02:53"}]}, {"id": "1be11590-dc61-42c2-a6f3-99c3569d4908", "equipment_id": "2202833", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Adaptor was found broken.\r\nReplaced with new power supply.", "created_at": "2025-07-20 09:13:51", "updated_at": "2025-07-20 09:13:51", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4312b537-d338-4307-81e6-0b3af8bc6735", "note_id": "1be11590-dc61-42c2-a6f3-99c3569d4908", "original_filename": "2202833.pdf", "stored_filename": "df714f81-a942-46d0-99bb-c5d134ee86d0.pdf", "file_path": "app/static/uploads/history/df714f81-a942-46d0-99bb-c5d134ee86d0.pdf", "mime_type": "application/pdf", "file_size": 1832047, "upload_date": "2025-07-20 09:13:51"}]}, {"id": "72740578-563d-472c-93cd-3901731d0ef2", "equipment_id": "HDHK51650", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked by the service call.\r\none of pedal is broken and replace already.\r\nThe unit is working in good condition.", "created_at": "2025-07-20 09:19:46", "updated_at": "2025-07-20 09:19:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8a2b5e97-1cf9-453b-b30d-489c81376eb8", "note_id": "72740578-563d-472c-93cd-3901731d0ef2", "original_filename": "HDHK51650.pdf", "stored_filename": "34916912-a853-47ba-a2aa-c6d31023780e.pdf", "file_path": "app/static/uploads/history/34916912-a853-47ba-a2aa-c6d31023780e.pdf", "mime_type": "application/pdf", "file_size": 506016, "upload_date": "2025-07-20 09:19:46"}]}, {"id": "7ac1b4dc-73cb-46bf-8ebc-8deb8a27d6a3", "equipment_id": "19101628", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "No laser shots detected. G-Runner unit, along with its accessories, was sent to the company for detailed inspection. Unit has been returned. Issue was identified as a faulty interface. a standby interface was provided and installed. Unit is now functioning properly.", "created_at": "2025-07-20 09:43:12", "updated_at": "2025-07-20 09:43:12", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4c51759c-5a01-4460-8c75-09ea6f8936f0", "note_id": "7ac1b4dc-73cb-46bf-8ebc-8deb8a27d6a3", "original_filename": "19101628.pdf", "stored_filename": "38a73e23-3d8e-43a0-8faa-eb02397de72d.pdf", "file_path": "app/static/uploads/history/38a73e23-3d8e-43a0-8faa-eb02397de72d.pdf", "mime_type": "application/pdf", "file_size": 1317727, "upload_date": "2025-07-20 09:43:12"}]}, {"id": "09d268bb-a8aa-4b63-9da4-9999c0997d41", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "System software requires an update. \r\nQuotation for the new software will be provided.", "created_at": "2025-07-20 09:51:19", "updated_at": "2025-07-20 09:51:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "40cfcb9d-8dda-41a0-9819-a20debf42c1c", "note_id": "09d268bb-a8aa-4b63-9da4-9999c0997d41", "original_filename": "16100425.pdf", "stored_filename": "43932a3a-6a48-46e2-9244-8900ec6f6df1.pdf", "file_path": "app/static/uploads/history/43932a3a-6a48-46e2-9244-8900ec6f6df1.pdf", "mime_type": "application/pdf", "file_size": 522016, "upload_date": "2025-07-20 09:51:19"}]}, {"id": "a8cb6e6a-080e-4840-ac5f-07b71e9d178f", "equipment_id": "19101628", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Suspected issue with the G-Runner interface module.\r\n<PERSON><PERSON><PERSON> was taken to the company for further evaluation.", "created_at": "2025-07-20 09:54:28", "updated_at": "2025-07-20 09:54:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d25be5fa-cfd7-4149-b5c5-dcffb49dc89e", "note_id": "a8cb6e6a-080e-4840-ac5f-07b71e9d178f", "original_filename": "19101628-_b.pdf", "stored_filename": "ff13a8a7-a982-4837-b9f4-dedfd4aae67c.pdf", "file_path": "app/static/uploads/history/ff13a8a7-a982-4837-b9f4-dedfd4aae67c.pdf", "mime_type": "application/pdf", "file_size": 803358, "upload_date": "2025-07-20 09:54:28"}]}, {"id": "654a7528-03fe-408d-a0c1-ad15a94583af", "equipment_id": "EO140051-0001", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Bed motor was found not functioning. \r\nControl unit was reset, then checked and tested.\r\nUnit is now operating normally.", "created_at": "2025-07-20 09:59:06", "updated_at": "2025-07-20 09:59:06", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "840e0058-25ed-4b66-a8a8-29eb45250415", "note_id": "654a7528-03fe-408d-a0c1-ad15a94583af", "original_filename": "EO140051.pdf", "stored_filename": "8baf32ae-d3dd-431e-ad7f-b5e9fcc2a64b.pdf", "file_path": "app/static/uploads/history/8baf32ae-d3dd-431e-ad7f-b5e9fcc2a64b.pdf", "mime_type": "application/pdf", "file_size": 506080, "upload_date": "2025-07-20 09:59:06"}]}, {"id": "22c688d4-5c33-4d16-9d52-25b2cfb9b6bf", "equipment_id": "GN50003378-0001", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Trolley brake was not functioning, and headrest was unstable. \r\nReplaced wheels and repaired the headrest. \r\nUnit tested and confirmed to be working properly.", "created_at": "2025-07-20 13:18:02", "updated_at": "2025-07-20 13:18:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a2266a21-425f-46b3-bae8-2a606c0527ca", "note_id": "22c688d4-5c33-4d16-9d52-25b2cfb9b6bf", "original_filename": "GN50003378-0001.pdf", "stored_filename": "a85ff2bb-c204-404c-8bcb-ecca88fa7029.pdf", "file_path": "app/static/uploads/history/a85ff2bb-c204-404c-8bcb-ecca88fa7029.pdf", "mime_type": "application/pdf", "file_size": 532944, "upload_date": "2025-07-20 13:18:02"}]}, {"id": "42a0d6db-4d82-4426-891e-cebb14d246a5", "equipment_id": "212060645", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found faulty battery on the defibrillator.\r\nReplaced with a new battery. \r\nPerformed daily test and verified all functions.\r\nunit is now working properly.", "created_at": "2025-07-20 13:27:12", "updated_at": "2025-07-20 13:27:12", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a828fd7b-9f8e-4f30-a1af-46d22183dc92", "note_id": "42a0d6db-4d82-4426-891e-cebb14d246a5", "original_filename": "212060645.pdf", "stored_filename": "6481a22d-1220-4334-b319-d0da23b990ad.pdf", "file_path": "app/static/uploads/history/6481a22d-1220-4334-b319-d0da23b990ad.pdf", "mime_type": "application/pdf", "file_size": 527056, "upload_date": "2025-07-20 13:27:12"}]}, {"id": "05a2e57b-f8cc-4553-ba09-298c6db3ac83", "equipment_id": "EO140051-0001", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Repaired the unit and reset the controller. \r\nAll function were tested and confirmed to be working properly.", "created_at": "2025-07-20 13:32:31", "updated_at": "2025-07-20 13:32:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2aeaa8c3-839f-405f-ab52-98fc3c750f76", "note_id": "05a2e57b-f8cc-4553-ba09-298c6db3ac83", "original_filename": "EO140051-0001.pdf", "stored_filename": "8c176325-624e-4d88-a314-5b6209369efb.pdf", "file_path": "app/static/uploads/history/8c176325-624e-4d88-a314-5b6209369efb.pdf", "mime_type": "application/pdf", "file_size": 487808, "upload_date": "2025-07-20 13:32:31"}]}, {"id": "7cc79ff4-3ed5-402a-b476-51e6e2c5d70a", "equipment_id": "GN50003378-0001", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Found one iron valve out of place and adjusted it.\r\nInspected all crews and noted one screw missing.\r\nUnit was checked and is functioning properly on one side.", "created_at": "2025-07-20 13:38:22", "updated_at": "2025-07-20 13:38:22", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3f0565b6-53aa-496b-ba9b-c87801d20889", "note_id": "7cc79ff4-3ed5-402a-b476-51e6e2c5d70a", "original_filename": "GN50003378-0001-_b.pdf", "stored_filename": "cf2b1678-2254-432d-81d1-b26b28c0787a.pdf", "file_path": "app/static/uploads/history/cf2b1678-2254-432d-81d1-b26b28c0787a.pdf", "mime_type": "application/pdf", "file_size": 507792, "upload_date": "2025-07-20 13:38:22"}]}, {"id": "bd70f4ad-68e9-46ba-b06b-8795e8e1063e", "equipment_id": "P770814040004", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found limb cuff connector (machine side) broken. \r\nReplaced the connector using a part from a spare machine. \r\nUnit tested and confirmed to be working properly.", "created_at": "2025-07-20 13:43:24", "updated_at": "2025-07-20 13:43:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "932c4a5a-a4ff-424b-b2f4-7a14a51fd0e6", "note_id": "bd70f4ad-68e9-46ba-b06b-8795e8e1063e", "original_filename": "P770814040004-_b.pdf", "stored_filename": "1eeca3e7-92e0-4493-8f11-d4ef8650becd.pdf", "file_path": "app/static/uploads/history/1eeca3e7-92e0-4493-8f11-d4ef8650becd.pdf", "mime_type": "application/pdf", "file_size": 521376, "upload_date": "2025-07-20 13:43:24"}]}, {"id": "b6d17400-aff5-4677-a1cd-84bdfa53445b", "equipment_id": "GN50003378-0004", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Headrest was found to be unstable. \r\nAdjusted the spring mechanism. \r\nUnit is currently working but will remain under observation for stability.", "created_at": "2025-07-20 13:55:31", "updated_at": "2025-07-20 13:55:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "eb5b3837-3038-4520-a427-67e58811148f", "note_id": "b6d17400-aff5-4677-a1cd-84bdfa53445b", "original_filename": "GN50003378-0004.pdf", "stored_filename": "804d221e-0014-4668-a419-12063417f629.pdf", "file_path": "app/static/uploads/history/804d221e-0014-4668-a419-12063417f629.pdf", "mime_type": "application/pdf", "file_size": 536144, "upload_date": "2025-07-20 13:55:31"}]}, {"id": "827040ce-a03e-461b-835b-1397571fe3eb", "equipment_id": "GN50003378-0002", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Headrest not working.\r\nRepaired unit.\r\nTested and confirmed functioning.", "created_at": "2025-07-20 13:57:23", "updated_at": "2025-07-20 13:57:23", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fa3d1454-a72b-4299-ae62-0d7b8a938357", "note_id": "827040ce-a03e-461b-835b-1397571fe3eb", "original_filename": "GN50003378-0002.pdf", "stored_filename": "11b74580-1808-491f-84b1-0269479be780.pdf", "file_path": "app/static/uploads/history/11b74580-1808-491f-84b1-0269479be780.pdf", "mime_type": "application/pdf", "file_size": 470608, "upload_date": "2025-07-20 13:57:23"}]}, {"id": "b21f812a-40c1-4698-be2b-ff366fae225c", "equipment_id": "*************", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit found to be functioning normally. \r\nBattery is weak- unit placed on charge for recovery.", "created_at": "2025-07-20 14:01:08", "updated_at": "2025-07-20 14:01:08", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2b6997f0-80bd-4a36-8f60-fda86695fd83", "note_id": "b21f812a-40c1-4698-be2b-ff366fae225c", "original_filename": "*************.pdf", "stored_filename": "4586b0e7-de22-48c7-ad6b-de2863e45c47.pdf", "file_path": "app/static/uploads/history/4586b0e7-de22-48c7-ad6b-de2863e45c47.pdf", "mime_type": "application/pdf", "file_size": 479568, "upload_date": "2025-07-20 14:01:08"}]}, {"id": "c7b1a14c-a163-42c8-b16a-b2f6eb4b9795", "equipment_id": "C800071048", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "ECG was tested using a simulator and found to have a faulty cable. \r\nReplaced the ECG cable.\r\nUnit is tested and confirmed to be working properly.", "created_at": "2025-07-20 14:04:46", "updated_at": "2025-07-20 14:04:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "22193a76-cd30-4bb0-832e-4741c07cc39d", "note_id": "c7b1a14c-a163-42c8-b16a-b2f6eb4b9795", "original_filename": "C800071048.pdf", "stored_filename": "1bc03cda-c1cf-46ed-94ec-f63d6d17154c.pdf", "file_path": "app/static/uploads/history/1bc03cda-c1cf-46ed-94ec-f63d6d17154c.pdf", "mime_type": "application/pdf", "file_size": 443088, "upload_date": "2025-07-20 14:04:46"}]}, {"id": "e262996e-84ae-4f56-9780-d30390f1b78e", "equipment_id": "58460", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Height adjustment was not functioning.\r\nRepaired the control unit and resolved the issue.\r\nUnit tested and is now working properly.", "created_at": "2025-07-20 14:25:41", "updated_at": "2025-07-20 14:25:41", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f49ac159-1673-41c8-9715-fd521fbf2eff", "note_id": "e262996e-84ae-4f56-9780-d30390f1b78e", "original_filename": "58460.pdf", "stored_filename": "80487acc-31e4-4c09-93fb-330d123e73e6.pdf", "file_path": "app/static/uploads/history/80487acc-31e4-4c09-93fb-330d123e73e6.pdf", "mime_type": "application/pdf", "file_size": 1318351, "upload_date": "2025-07-20 14:25:41"}]}, {"id": "4cf2a7b0-5927-4551-ad1a-0a341bdfd7ea", "equipment_id": "1.00002E-plus12", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Warrantee Visit", "created_at": "2025-07-20 14:40:54", "updated_at": "2025-07-20 14:40:54", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a8e2364e-21c5-4912-b4f7-5db098566a93", "note_id": "4cf2a7b0-5927-4551-ad1a-0a341bdfd7ea", "original_filename": "1.00002E12.pdf", "stored_filename": "94d05f53-cf8d-4b73-bbfc-e671dab9ce08.pdf", "file_path": "app/static/uploads/history/94d05f53-cf8d-4b73-bbfc-e671dab9ce08.pdf", "mime_type": "application/pdf", "file_size": 821326, "upload_date": "2025-07-20 14:40:54"}]}, {"id": "4082ea0e-cd33-4f8c-a897-b417b15520bb", "equipment_id": "E0130018", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Identified defective bed control switch due to a technical issue. Repaired using genuine components. Unit tested and confirmed to be functioning properly.", "created_at": "2025-07-20 14:52:45", "updated_at": "2025-07-20 14:52:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ff39f853-0171-4f19-ac77-5d0d253c54dd", "note_id": "4082ea0e-cd33-4f8c-a897-b417b15520bb", "original_filename": "EO130018.pdf", "stored_filename": "142906a8-524e-4fb9-9d3d-c12016b2e502.pdf", "file_path": "app/static/uploads/history/142906a8-524e-4fb9-9d3d-c12016b2e502.pdf", "mime_type": "application/pdf", "file_size": 1519567, "upload_date": "2025-07-20 14:52:45"}]}, {"id": "218daebc-da26-4178-861c-e439750ca3e8", "equipment_id": "HDHK51649", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\nThe unit airflow O₂ is not working.\r\nNeed to check Auto Supply and change the part.", "created_at": "2025-07-20 15:30:56", "updated_at": "2025-07-20 15:30:56", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7dcb8816-f2c9-4672-81c0-74ebb657dd2e", "note_id": "218daebc-da26-4178-861c-e439750ca3e8", "original_filename": "HDHK51649.pdf", "stored_filename": "ad9a12dc-df97-4529-97c3-33df279256ba.pdf", "file_path": "app/static/uploads/history/ad9a12dc-df97-4529-97c3-33df279256ba.pdf", "mime_type": "application/pdf", "file_size": 762384, "upload_date": "2025-07-20 15:30:56"}]}, {"id": "acb27eac-2cfa-4ba3-abde-d6a997d89209", "equipment_id": "HDHT50691", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\nThe pedal pad is functioning and working well.\r\nThe cover of the pad is broken and needs to be changed.", "created_at": "2025-07-20 15:35:45", "updated_at": "2025-07-20 15:35:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "15b3eae8-c0c1-4ddf-8d49-deedeb1596fd", "note_id": "acb27eac-2cfa-4ba3-abde-d6a997d89209", "original_filename": "HDHT50691.pdf", "stored_filename": "ef79179c-49a1-43e8-aded-fb44dac5d74f.pdf", "file_path": "app/static/uploads/history/ef79179c-49a1-43e8-aded-fb44dac5d74f.pdf", "mime_type": "application/pdf", "file_size": 774960, "upload_date": "2025-07-20 15:35:45"}]}, {"id": "8b43e216-7d87-4a06-b96a-f9a3091c3855", "equipment_id": "80091176", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "ECG cable had  a loose contact. \r\nIssue was resolved.\r\nUnit tested and now functioning properly.", "created_at": "2025-07-20 15:36:18", "updated_at": "2025-07-20 15:36:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d6bb1614-1bfd-49a6-8824-adb118684c35", "note_id": "8b43e216-7d87-4a06-b96a-f9a3091c3855", "original_filename": "80091176-_b.pdf", "stored_filename": "e92aee30-a570-497d-8e86-9f80d7869403.pdf", "file_path": "app/static/uploads/history/e92aee30-a570-497d-8e86-9f80d7869403.pdf", "mime_type": "application/pdf", "file_size": 511168, "upload_date": "2025-07-20 15:36:18"}]}, {"id": "31b42683-b3ef-4b37-aa7d-51072300a563", "equipment_id": "HDHK51647", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "date: 15/01/2025\r\n\r\nunit is checked.\r\nThe unit screen is shady and not clear.\r\nThe unit is not working well and needs to be changed with a new screen/display board.\r\n\r\ndate: 11/3/2025\r\n\r\nunit is checked.\r\nReplace a spare part for the unit.\r\nunit is working in good condition.", "created_at": "2025-07-20 16:01:58", "updated_at": "2025-07-20 16:01:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e5723054-29da-4fc2-8031-bee12489d0ba", "note_id": "31b42683-b3ef-4b37-aa7d-51072300a563", "original_filename": "HDHK51647.pdf", "stored_filename": "7b9d5098-c221-4ae2-a6a1-9941a8f0069f.pdf", "file_path": "app/static/uploads/history/7b9d5098-c221-4ae2-a6a1-9941a8f0069f.pdf", "mime_type": "application/pdf", "file_size": 1295775, "upload_date": "2025-07-20 16:01:58"}]}, {"id": "1800c912-5b4d-4ef9-8dbe-3b964716a332", "equipment_id": "HDHN50630", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Date: 16/01/2025\r\n\r\nunit is checked.\r\nThe humidifier is not working, and the humidity has an error.\r\nThe humidifier needs to be changed with a new one.\r\n\r\nDate: 11/3/2025\r\n\r\nunits is checked.\r\nhumidity is not working.\r\nThe humidity fuse is checked in the relay board and changed.\r\nunit is working in good condition.", "created_at": "2025-07-20 16:07:13", "updated_at": "2025-07-20 16:07:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5d51c89a-827d-4927-a00e-6389d54b2e8f", "note_id": "1800c912-5b4d-4ef9-8dbe-3b964716a332", "original_filename": "HDHN50630.pdf", "stored_filename": "836fdc88-783b-4e91-8a38-bfdfa8438d26.pdf", "file_path": "app/static/uploads/history/836fdc88-783b-4e91-8a38-bfdfa8438d26.pdf", "mime_type": "application/pdf", "file_size": 1287007, "upload_date": "2025-07-20 16:07:13"}]}, {"id": "d5bea2fd-614a-41b1-9e72-412429a1f414", "equipment_id": "HDHT50690", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\nThe window and lock are broken but fixed already, and it's in good condition.", "created_at": "2025-07-20 16:09:53", "updated_at": "2025-07-20 16:09:53", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "620634d8-9f20-4644-abe9-9da7f70c461d", "note_id": "d5bea2fd-614a-41b1-9e72-412429a1f414", "original_filename": "HDHT50690.pdf", "stored_filename": "fe727d2a-de57-4524-befa-dec401340488.pdf", "file_path": "app/static/uploads/history/fe727d2a-de57-4524-befa-dec401340488.pdf", "mime_type": "application/pdf", "file_size": 775758, "upload_date": "2025-07-20 16:09:53"}]}, {"id": "e7ec9732-677a-4a96-8be7-0eaef81096a8", "equipment_id": "HDHS50195", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\n2 window locks are broken, and they are changed with extra parts.\r\nunit is in good condition.", "created_at": "2025-07-20 16:13:43", "updated_at": "2025-07-20 16:13:43", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "399cc1b7-1e7f-4ff2-a488-4d3c70f7a1c6", "note_id": "e7ec9732-677a-4a96-8be7-0eaef81096a8", "original_filename": "HDHS50195.pdf", "stored_filename": "a2b439b3-3fe0-49fe-acb0-42fab78dd64a.pdf", "file_path": "app/static/uploads/history/a2b439b3-3fe0-49fe-acb0-42fab78dd64a.pdf", "mime_type": "application/pdf", "file_size": 795678, "upload_date": "2025-07-20 16:13:43"}]}, {"id": "180371a2-105d-488e-b7df-14d5b0256540", "equipment_id": "HDHN50528", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\nThe bed has a problem with the lock because it's not locking.\r\nThe bed lock is already fixed. and working.\r\nunit is in good condition.", "created_at": "2025-07-20 16:17:45", "updated_at": "2025-07-20 16:17:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "aef23bd8-0021-4b4e-8194-000f01fdc885", "note_id": "180371a2-105d-488e-b7df-14d5b0256540", "original_filename": "HDHN50528.pdf", "stored_filename": "7b5b5a15-1571-4c37-a5b8-d9ac85cad3f6.pdf", "file_path": "app/static/uploads/history/7b5b5a15-1571-4c37-a5b8-d9ac85cad3f6.pdf", "mime_type": "application/pdf", "file_size": 827950, "upload_date": "2025-07-20 16:17:45"}]}, {"id": "7abbc66a-e3ed-4093-99e1-1e1813f17293", "equipment_id": "58461", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Repaired the bed brake system by removing the wheels, cleaning the affected components, and reinstalling them.\r\nUnit tested and is now working properly.", "created_at": "2025-07-20 16:18:38", "updated_at": "2025-07-20 16:18:38", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a3ea8d14-142c-4853-a844-8e4d6e10ba65", "note_id": "7abbc66a-e3ed-4093-99e1-1e1813f17293", "original_filename": "58461.pdf", "stored_filename": "142396d3-2265-402a-98d5-848b79b1f5f4.pdf", "file_path": "app/static/uploads/history/142396d3-2265-402a-98d5-848b79b1f5f4.pdf", "mime_type": "application/pdf", "file_size": 488832, "upload_date": "2025-07-20 16:18:38"}]}, {"id": "8dabaa0c-8005-47ba-8d14-2ac479bdc016", "equipment_id": "560034-M19901960003", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Printing discrepancies in FHR. \r\nIssue will be coordinated with the supplier.", "created_at": "2025-07-20 16:24:58", "updated_at": "2025-07-20 16:24:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "35c93d64-2c7d-4407-8b4d-644eaa8356ac", "note_id": "8dabaa0c-8005-47ba-8d14-2ac479bdc016", "original_filename": "560034-M19901960003.pdf", "stored_filename": "4af1d96b-9e0a-4ad0-baff-04800dd3f972.pdf", "file_path": "app/static/uploads/history/4af1d96b-9e0a-4ad0-baff-04800dd3f972.pdf", "mime_type": "application/pdf", "file_size": 464256, "upload_date": "2025-07-20 16:24:58"}]}, {"id": "747d2144-3697-42e5-bcd5-3389cb0abd0e", "equipment_id": "16206556", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "SPO2 not functioning.\r\nInspected the unit, will coordinate with the manufacturer to resolve the problem.", "created_at": "2025-07-20 16:28:29", "updated_at": "2025-07-20 16:28:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2cdeb4ec-518d-42d1-b02b-ad871bc1edcf", "note_id": "747d2144-3697-42e5-bcd5-3389cb0abd0e", "original_filename": "16206556.pdf", "stored_filename": "352f2628-5e0a-4a25-b5d8-909e826cc9bf.pdf", "file_path": "app/static/uploads/history/352f2628-5e0a-4a25-b5d8-909e826cc9bf.pdf", "mime_type": "application/pdf", "file_size": 486624, "upload_date": "2025-07-20 16:28:29"}]}, {"id": "bb168983-2883-4994-adb6-ba13e208c339", "equipment_id": "HDHS50195", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\nThe unit is showing system failure 25, and this means it needs to change a fan motor.\r\nneeds to make a repair request.", "created_at": "2025-07-20 16:31:19", "updated_at": "2025-07-20 16:31:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b6f523fb-0023-457f-ae30-50019650f9db", "note_id": "bb168983-2883-4994-adb6-ba13e208c339", "original_filename": "HDHS50195_B.pdf", "stored_filename": "a9181cbd-e562-40d3-ac56-f27cdd6f4977.pdf", "file_path": "app/static/uploads/history/a9181cbd-e562-40d3-ac56-f27cdd6f4977.pdf", "mime_type": "application/pdf", "file_size": 460912, "upload_date": "2025-07-20 16:31:19"}]}, {"id": "8c5a0ddc-03fe-46e0-8280-c307f994d860", "equipment_id": "560034-M19901960003", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "FHR was not printing correctly. \r\nReset paper setting.\r\ntested the unit and confirmed working.", "created_at": "2025-07-20 16:33:52", "updated_at": "2025-07-20 16:33:52", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "817ca5c2-7340-40ea-bdb0-4c1792ffbb9c", "note_id": "8c5a0ddc-03fe-46e0-8280-c307f994d860", "original_filename": "560034-M19901960003-_b.pdf", "stored_filename": "dbf9c4fc-3314-4d24-9905-d9b55ad44fd8.pdf", "file_path": "app/static/uploads/history/dbf9c4fc-3314-4d24-9905-d9b55ad44fd8.pdf", "mime_type": "application/pdf", "file_size": 483808, "upload_date": "2025-07-20 16:33:52"}]}, {"id": "10b67b55-263a-4031-9954-b3c1d8b4b8eb", "equipment_id": "560034-M19901960002", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "FHR was not printing correctly. \r\nReset paper setting.\r\ntested the unit and confirmed working.", "created_at": "2025-07-20 16:34:29", "updated_at": "2025-07-20 16:34:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4f46ebdc-11ca-4dbc-92a3-cff8dc7fee62", "note_id": "10b67b55-263a-4031-9954-b3c1d8b4b8eb", "original_filename": "560034-M19901960002.pdf", "stored_filename": "c69bc9bc-e0c6-44cc-ba1c-3e1d3823bff7.pdf", "file_path": "app/static/uploads/history/c69bc9bc-e0c6-44cc-ba1c-3e1d3823bff7.pdf", "mime_type": "application/pdf", "file_size": 483808, "upload_date": "2025-07-20 16:34:29"}]}, {"id": "7c063377-8d62-4f43-bc67-8febbc751cc8", "equipment_id": "HDHK51649", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is checked.\r\nThe air probe of the unit is a failure.\r\nThe temperature of the machine is not increasing.\r\nunit is fixed, and temperature increased.\r\nunit is in good condition.", "created_at": "2025-07-20 16:35:25", "updated_at": "2025-07-20 16:35:25", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "826ec9b6-34fa-46fa-8f82-2937df5c9f9c", "note_id": "7c063377-8d62-4f43-bc67-8febbc751cc8", "original_filename": "HDHK51649_B.pdf", "stored_filename": "0a186ff6-d63d-4115-a77d-0292fbd4bbf2.pdf", "file_path": "app/static/uploads/history/0a186ff6-d63d-4115-a77d-0292fbd4bbf2.pdf", "mime_type": "application/pdf", "file_size": 739920, "upload_date": "2025-07-20 16:35:25"}]}, {"id": "34daeea9-73cb-4fa6-bd9c-d9718c2bfa29", "equipment_id": "5BM000271", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit was not charging. Battery module required a reset.\r\nPerformed reset on the battery module.\r\nUnit tested and confirmed to be working properly.", "created_at": "2025-07-20 16:43:05", "updated_at": "2025-07-20 16:43:05", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "151672af-588b-49cd-9dbe-103ceff52b0e", "note_id": "34daeea9-73cb-4fa6-bd9c-d9718c2bfa29", "original_filename": "5BM000271.pdf", "stored_filename": "86425bed-0a9b-4e84-83bf-009eee1abf57.pdf", "file_path": "app/static/uploads/history/86425bed-0a9b-4e84-83bf-009eee1abf57.pdf", "mime_type": "application/pdf", "file_size": 454992, "upload_date": "2025-07-20 16:43:05"}]}, {"id": "810748b9-7629-4c0e-a65c-37ad7d8562b2", "equipment_id": "212060645", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Battery not working.\r\nFormatted and reset battery settings.\r\nUnder observation, if problem persist requested to replace the battery.", "created_at": "2025-07-20 16:49:42", "updated_at": "2025-07-20 16:49:42", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d99e55f4-08da-4cd2-bd14-b2fc6cf6ea8d", "note_id": "810748b9-7629-4c0e-a65c-37ad7d8562b2", "original_filename": "212060645-_b.pdf", "stored_filename": "80a22070-d093-4c64-b6dc-22a57cc6e2cc.pdf", "file_path": "app/static/uploads/history/80a22070-d093-4c64-b6dc-22a57cc6e2cc.pdf", "mime_type": "application/pdf", "file_size": 1897999, "upload_date": "2025-07-20 16:49:42"}]}, {"id": "1dac1d1e-8ec1-40e3-9c0d-9785aa6c983b", "equipment_id": "212060645", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Defibrillator borrowed for 3-days demo period. \r\nUnit will be returned upon completion of the demo.", "created_at": "2025-07-20 16:57:47", "updated_at": "2025-07-20 16:57:47", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7a36d02a-dc77-46ed-9325-d721882662a0", "note_id": "1dac1d1e-8ec1-40e3-9c0d-9785aa6c983b", "original_filename": "212060645-_c.pdf", "stored_filename": "fdf22240-fa0f-478f-ada8-3d04e01f7d5a.pdf", "file_path": "app/static/uploads/history/fdf22240-fa0f-478f-ada8-3d04e01f7d5a.pdf", "mime_type": "application/pdf", "file_size": 509104, "upload_date": "2025-07-20 16:57:47"}]}, {"id": "65ebf196-aed2-4708-a001-e5c01f28d99e", "equipment_id": "212060645", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Battery failure observed. \r\nPerformed formatting.\r\nUnit will be rechecked- if the issue persist, battery replacement will be required.", "created_at": "2025-07-20 17:00:19", "updated_at": "2025-07-20 17:00:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "85cdf77c-739e-45e7-a21c-4ffba3c4f782", "note_id": "65ebf196-aed2-4708-a001-e5c01f28d99e", "original_filename": "212060645-_d.pdf", "stored_filename": "355fb741-9b25-4566-8cf5-bbeb7240da6c.pdf", "file_path": "app/static/uploads/history/355fb741-9b25-4566-8cf5-bbeb7240da6c.pdf", "mime_type": "application/pdf", "file_size": 490976, "upload_date": "2025-07-20 17:00:19"}]}, {"id": "fd9a2cd8-a1cb-4418-b13e-2d7f82b1373d", "equipment_id": "72210006", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Returned unit.", "created_at": "2025-07-20 17:35:30", "updated_at": "2025-07-20 17:35:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a529e26d-de78-4329-91d6-d495eb6cc382", "note_id": "fd9a2cd8-a1cb-4418-b13e-2d7f82b1373d", "original_filename": "72210006-_b.pdf", "stored_filename": "3fb2d7fb-ddad-4854-9ae7-7548ed236c14.pdf", "file_path": "app/static/uploads/history/3fb2d7fb-ddad-4854-9ae7-7548ed236c14.pdf", "mime_type": "application/pdf", "file_size": 481536, "upload_date": "2025-07-20 17:35:30"}]}, {"id": "63cea09e-8a4c-41fd-b2a0-fadb85877985", "equipment_id": "72210006", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit taken to the company for checking.", "created_at": "2025-07-20 17:36:46", "updated_at": "2025-07-20 17:36:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8d6c054d-ef60-4d69-b45b-47972e253dea", "note_id": "63cea09e-8a4c-41fd-b2a0-fadb85877985", "original_filename": "72210006-_c.pdf", "stored_filename": "9c31fba1-ed63-4bf5-8d1b-dfa7cbabf6e6.pdf", "file_path": "app/static/uploads/history/9c31fba1-ed63-4bf5-8d1b-dfa7cbabf6e6.pdf", "mime_type": "application/pdf", "file_size": 541104, "upload_date": "2025-07-20 17:36:46"}]}, {"id": "bf1bc30f-f694-490f-a1d7-32f468c8eccd", "equipment_id": "18X25721", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue: <PERSON><PERSON><PERSON> test- indicator color did not change.", "created_at": "2025-07-20 17:44:00", "updated_at": "2025-07-20 17:44:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7e76e1f8-4374-453a-b125-d2b9319ff3e4", "note_id": "bf1bc30f-f694-490f-a1d7-32f468c8eccd", "original_filename": "18X25721.pdf", "stored_filename": "d2848a92-8c63-497b-9e56-8b9f2b468756.pdf", "file_path": "app/static/uploads/history/d2848a92-8c63-497b-9e56-8b9f2b468756.pdf", "mime_type": "application/pdf", "file_size": 508176, "upload_date": "2025-07-20 17:44:00"}]}, {"id": "dcd92542-4419-4263-bbab-6d383d246e19", "equipment_id": "18X25721", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue: Machine stopped working during cycle.\r\nUpon inspection, two fuses were found damaged (cable power side and stock side).\r\nReplaced the damaged fuses.\r\nUnit tested and is now  working properly.", "created_at": "2025-07-20 17:51:27", "updated_at": "2025-07-20 17:51:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b6a31343-0b3b-4fec-b0bb-f0ceea9aeac0", "note_id": "dcd92542-4419-4263-bbab-6d383d246e19", "original_filename": "18X25721-_b.pdf", "stored_filename": "914d706b-492f-4796-bc01-b8afe601decd.pdf", "file_path": "app/static/uploads/history/914d706b-492f-4796-bc01-b8afe601decd.pdf", "mime_type": "application/pdf", "file_size": 508704, "upload_date": "2025-07-20 17:51:27"}]}, {"id": "ddb41587-4ae8-4760-87e6-4d0fdc428693", "equipment_id": "18X25721", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Chamber level error detected. \r\nCleaned the level sensor and the filter.\r\nPerformed one cycle- complete successfully.\r\n-unit working properly", "created_at": "2025-07-20 17:57:51", "updated_at": "2025-07-20 17:57:51", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2047a199-391a-43ef-b137-8f6501d4b16c", "note_id": "ddb41587-4ae8-4760-87e6-4d0fdc428693", "original_filename": "18X25721-_c.pdf", "stored_filename": "bec6d48c-c1a1-4bf4-9e9c-d59ec9e79023.pdf", "file_path": "app/static/uploads/history/bec6d48c-c1a1-4bf4-9e9c-d59ec9e79023.pdf", "mime_type": "application/pdf", "file_size": 521056, "upload_date": "2025-07-20 17:57:51"}]}, {"id": "217c89a2-69e7-44bd-93db-3e2bc3572ab4", "equipment_id": "4C08854", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is reconnected and refixed.\r\ninsulated the lamp cables and cleaned the lamp connectors.\r\nall connections are tested okay\r\nunit is working well.", "created_at": "2025-07-21 08:34:34", "updated_at": "2025-07-21 08:34:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9c820a40-5084-4fea-b881-3930db533583", "note_id": "217c89a2-69e7-44bd-93db-3e2bc3572ab4", "original_filename": "4C08854.pdf", "stored_filename": "65d57dd7-d5fe-413e-8a36-d864092560c7.pdf", "file_path": "app/static/uploads/history/65d57dd7-d5fe-413e-8a36-d864092560c7.pdf", "mime_type": "application/pdf", "file_size": 477248, "upload_date": "2025-07-21 08:34:34"}]}, {"id": "c85b8886-0472-48b6-8102-5ee00f87a4a6", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The unit is inspected, and a reset is done for the unloading side doors.\r\nThe unit has run a full cycle and is working properly.", "created_at": "2025-07-21 08:58:39", "updated_at": "2025-07-21 08:59:53", "last_modified_by": "admin", "last_modified_by_name": "admin", "is_edited": true, "attachments": [{"id": "03ba6df7-f935-483d-86ba-3afecd251666", "note_id": "c85b8886-0472-48b6-8102-5ee00f87a4a6", "original_filename": "230506.pdf", "stored_filename": "0033d953-8e32-44c8-92cb-ad4ac6c70a59.pdf", "file_path": "app/static/uploads/history/0033d953-8e32-44c8-92cb-ad4ac6c70a59.pdf", "mime_type": "application/pdf", "file_size": 1945375, "upload_date": "2025-07-21 08:58:39"}]}, {"id": "b72b04b6-21fa-4769-886c-acf035f55697", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked and found the unloading door not opened.\r\nAfter the cycle, suspected problems with the door gasket and relay module.\r\nReplace the door gasket and relay module.\r\nOne cycle is fully completed, all connections are tested okay, and the system is working well.\r\nDue to a technical problem, the unloading door is not open.", "created_at": "2025-07-21 09:20:13", "updated_at": "2025-07-21 09:20:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9b1535a9-1d0d-4002-b54b-0cd648930b00", "note_id": "b72b04b6-21fa-4769-886c-acf035f55697", "original_filename": "230506_D.pdf", "stored_filename": "49fa96d7-6a75-4ce1-99b8-e652a05e3723.pdf", "file_path": "app/static/uploads/history/49fa96d7-6a75-4ce1-99b8-e652a05e3723.pdf", "mime_type": "application/pdf", "file_size": 548624, "upload_date": "2025-07-21 09:20:13"}]}, {"id": "898b20c0-f85f-4bec-b44a-9a18952369f9", "equipment_id": "230504", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Modification of washer disinfection supply line.\r\nWater supply connected to water system.", "created_at": "2025-07-21 09:25:44", "updated_at": "2025-07-21 09:25:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "13631534-cf02-4992-b654-cdec1bb24e3f", "note_id": "898b20c0-f85f-4bec-b44a-9a18952369f9", "original_filename": "230504.pdf", "stored_filename": "2571aac9-11ed-4b91-a178-e8efb560ddc2.pdf", "file_path": "app/static/uploads/history/2571aac9-11ed-4b91-a178-e8efb560ddc2.pdf", "mime_type": "application/pdf", "file_size": 751630, "upload_date": "2025-07-21 09:25:44"}]}, {"id": "b9bbf16e-3171-48cc-8fa3-bb56105b3eea", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspect the unit.\r\nThe door relay is faulty.\r\nReplace the relay.\r\nunit is working well.", "created_at": "2025-07-21 09:29:23", "updated_at": "2025-07-21 09:29:23", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "635fe462-3d6f-452d-9da8-f55d5b8ee315", "note_id": "b9bbf16e-3171-48cc-8fa3-bb56105b3eea", "original_filename": "230506_E.pdf", "stored_filename": "e0137324-baf8-4f89-bedf-fedc92325f0b.pdf", "file_path": "app/static/uploads/history/e0137324-baf8-4f89-bedf-fedc92325f0b.pdf", "mime_type": "application/pdf", "file_size": 816318, "upload_date": "2025-07-21 09:29:23"}]}, {"id": "82d9a517-feeb-4cf6-9662-5a141587b228", "equipment_id": "60150147", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the three centrifuge lid locks.\r\nchecked and confirmed the three units are working properly.", "created_at": "2025-07-21 14:14:24", "updated_at": "2025-07-21 14:14:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "be53f682-c74e-4aec-8908-cbec3d3fc53c", "note_id": "82d9a517-feeb-4cf6-9662-5a141587b228", "original_filename": "60150147.pdf", "stored_filename": "4dcbce54-67a5-48b7-aebd-bd8865c83b28.pdf", "file_path": "app/static/uploads/history/4dcbce54-67a5-48b7-aebd-bd8865c83b28.pdf", "mime_type": "application/pdf", "file_size": 1980927, "upload_date": "2025-07-21 14:14:24"}]}, {"id": "b834cb43-1fac-4141-9440-f70555b359bc", "equipment_id": "17866", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "check the instruments\r\nCheck and clean the internal parts.\r\nClean transducer and RBC clogs removed.\r\nFlush reaction chambers with cell clean manually.\r\ndrained reaction chambers and waste fluid chamber.\r\nRinse the flow cell, and air bubbles are removed.\r\nCheck the FCM syringe, sensors, and motors.", "created_at": "2025-07-21 14:22:31", "updated_at": "2025-07-21 14:22:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a24a2b80-4856-44a4-96c4-2b30d33681e8", "note_id": "b834cb43-1fac-4141-9440-f70555b359bc", "original_filename": "17866.pdf", "stored_filename": "4dcb6623-7155-47cb-ac66-aa02d7e9361f.pdf", "file_path": "app/static/uploads/history/4dcb6623-7155-47cb-ac66-aa02d7e9361f.pdf", "mime_type": "application/pdf", "file_size": 541792, "upload_date": "2025-07-21 14:22:31"}]}, {"id": "d07cc240-63b4-4646-aa2f-3eb83e3bcd31", "equipment_id": "E085D0060", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "found the SPO2 probe defective.\r\nreplaced the SPO2 probe and confirmed the unit is working well.", "created_at": "2025-07-21 14:53:17", "updated_at": "2025-07-21 14:53:17", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e7632869-b91e-4470-bffa-088c87bca4a1", "note_id": "d07cc240-63b4-4646-aa2f-3eb83e3bcd31", "original_filename": "E085D0060.pdf", "stored_filename": "47b668bd-164a-494a-ad00-7980ee4c88d6.pdf", "file_path": "app/static/uploads/history/47b668bd-164a-494a-ad00-7980ee4c88d6.pdf", "mime_type": "application/pdf", "file_size": 1956655, "upload_date": "2025-07-21 14:53:17"}]}, {"id": "c6f018a4-3433-43ad-be24-bba84cb269ea", "equipment_id": "21100100093", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is inspected and checked the NIBP. Swap it from another machine, and the unit is still not working.\r\nThe NIBP module is faulty.\r\nConfirm the manufacturer and wait for another quotation.", "created_at": "2025-07-21 16:53:48", "updated_at": "2025-07-21 16:53:48", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "412286b1-e234-4642-8f68-ff4f460ebf3a", "note_id": "c6f018a4-3433-43ad-be24-bba84cb269ea", "original_filename": "211000100093.pdf", "stored_filename": "a408c2d8-a571-4220-82e7-d41881faefa3.pdf", "file_path": "app/static/uploads/history/a408c2d8-a571-4220-82e7-d41881faefa3.pdf", "mime_type": "application/pdf", "file_size": 836222, "upload_date": "2025-07-21 16:53:48"}]}, {"id": "c375a01b-1db8-4195-8031-fdb736d56609", "equipment_id": "56034-M1990190001", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and suspected that the printer module has a problem.\r\nmanufacturer need to check the unit.", "created_at": "2025-07-21 17:42:49", "updated_at": "2025-07-21 17:42:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ecb85449-31e8-4fad-8a4a-6c88a985f424", "note_id": "c375a01b-1db8-4195-8031-fdb736d56609", "original_filename": "56034-M1990190001.pdf", "stored_filename": "bb1b601e-ea15-4ecd-a937-18ecff880561.pdf", "file_path": "app/static/uploads/history/bb1b601e-ea15-4ecd-a937-18ecff880561.pdf", "mime_type": "application/pdf", "file_size": 526992, "upload_date": "2025-07-21 17:42:49"}]}, {"id": "91c89ee5-695e-4b32-9dc2-54250aae60a5", "equipment_id": "460323-M160150003", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit checked and the print speed.\r\nThe print speed of 1 cm/min changed into 3 cm/min.\r\nThe machine is working properly.", "created_at": "2025-07-21 17:54:35", "updated_at": "2025-07-21 17:54:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c4dbda20-bf48-4865-ae71-edb005716bc0", "note_id": "91c89ee5-695e-4b32-9dc2-54250aae60a5", "original_filename": "M460323-M16015003.pdf", "stored_filename": "7730a224-4cff-4dc9-a2c1-cb5db58c2a23.pdf", "file_path": "app/static/uploads/history/7730a224-4cff-4dc9-a2c1-cb5db58c2a23.pdf", "mime_type": "application/pdf", "file_size": 490096, "upload_date": "2025-07-21 17:54:35"}]}, {"id": "62a0c71b-fc59-463b-b43e-faa9b07f8b4e", "equipment_id": "205009262", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit shifted from Hala Clinic to Alorf Hospital and connected with air and water supplies.", "created_at": "2025-07-22 06:15:26", "updated_at": "2025-07-22 06:15:26", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9b3fbc5c-622b-49bb-b43f-a7a38b549d0e", "note_id": "62a0c71b-fc59-463b-b43e-faa9b07f8b4e", "original_filename": "205009262.pdf", "stored_filename": "7edcdba7-f87d-4e0a-a0e0-b0dc00db9ec5.pdf", "file_path": "app/static/uploads/history/7edcdba7-f87d-4e0a-a0e0-b0dc00db9ec5.pdf", "mime_type": "application/pdf", "file_size": 478448, "upload_date": "2025-07-22 06:15:26"}]}, {"id": "d29585c3-b8f0-4514-866e-61e04a16e8c8", "equipment_id": "2018PRO-plus1081", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Detected water leakage from the drain valve. \r\nFound valves clogged with impurities. \r\nCleaned the filter and valve. \r\nPerformed test cycle—unit completed one cycle successfully and is now functioning properly.", "created_at": "2025-07-22 06:21:08", "updated_at": "2025-07-22 06:21:08", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1d35b43f-f78c-4823-bcb2-2d899650026a", "note_id": "d29585c3-b8f0-4514-866e-61e04a16e8c8", "original_filename": "2018PRO1081.pdf", "stored_filename": "4dcc31d9-ae76-45f9-8ae5-4fe5f9260250.pdf", "file_path": "app/static/uploads/history/4dcc31d9-ae76-45f9-8ae5-4fe5f9260250.pdf", "mime_type": "application/pdf", "file_size": 538688, "upload_date": "2025-07-22 06:21:08"}]}, {"id": "004099a0-5071-4a0d-a451-56973fca11a7", "equipment_id": "18X25721", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified a broken fuse; replacement with a new one is required.", "created_at": "2025-07-22 06:26:11", "updated_at": "2025-07-22 06:26:11", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d9f3ed04-d911-421b-afb2-5d27f1021109", "note_id": "004099a0-5071-4a0d-a451-56973fca11a7", "original_filename": "18X25721-_d.pdf", "stored_filename": "5877d688-f0b0-4dfd-9557-6ba6df8d0cb6.pdf", "file_path": "app/static/uploads/history/5877d688-f0b0-4dfd-9557-6ba6df8d0cb6.pdf", "mime_type": "application/pdf", "file_size": 471872, "upload_date": "2025-07-22 06:26:11"}]}, {"id": "3523c5c6-b2b8-45a6-b183-13671baf21ba", "equipment_id": "18X25721", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found a blown fuse inside. \r\nReplaced the fuse and tested the unit- functioning properly.", "created_at": "2025-07-22 06:29:56", "updated_at": "2025-07-22 06:29:56", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "64aea1be-2de7-4f18-9f2d-d532f0463f5e", "note_id": "3523c5c6-b2b8-45a6-b183-13671baf21ba", "original_filename": "18X25721-_e.pdf", "stored_filename": "d2b4135d-381b-4a9c-921c-1b4299b14826.pdf", "file_path": "app/static/uploads/history/d2b4135d-381b-4a9c-921c-1b4299b14826.pdf", "mime_type": "application/pdf", "file_size": 492432, "upload_date": "2025-07-22 06:29:56"}]}, {"id": "6c8963cb-2616-496b-94d6-3b941721d377", "equipment_id": "2112438", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found suction hose blocked with patient debris. \r\nCleaned the hose—unit tested and functioning properly.", "created_at": "2025-07-22 06:46:18", "updated_at": "2025-07-22 06:46:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9eda7ebb-3e96-4ac5-9e47-2cfae3c1f689", "note_id": "6c8963cb-2616-496b-94d6-3b941721d377", "original_filename": "2112438.pdf", "stored_filename": "3798962f-c87b-4a7b-9460-f089f1d311ff.pdf", "file_path": "app/static/uploads/history/3798962f-c87b-4a7b-9460-f089f1d311ff.pdf", "mime_type": "application/pdf", "file_size": 497536, "upload_date": "2025-07-22 06:46:18"}]}, {"id": "4a96176d-444a-4d30-9d8f-1eb61addba83", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The unloading door was checked and found not open.\r\nAfter the cycle, suspected problem with the door gasket and relay module.\r\nReplace the door gasket and relay module, and one cycle is completed.\r\nAll connections are tested okay, and the system is working well.\r\nThe unloading door is not open due to a technical problem.", "created_at": "2025-07-22 07:04:16", "updated_at": "2025-07-22 07:04:16", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5f587357-035e-4998-8f15-6bf6889c4e13", "note_id": "4a96176d-444a-4d30-9d8f-1eb61addba83", "original_filename": "230506_B.pdf", "stored_filename": "a9648b05-a9c8-4d61-bfd6-75b5f98215f5.pdf", "file_path": "app/static/uploads/history/a9648b05-a9c8-4d61-bfd6-75b5f98215f5.pdf", "mime_type": "application/pdf", "file_size": 1900927, "upload_date": "2025-07-22 07:04:16"}]}, {"id": "214a07a1-9a0c-4f05-b7c8-889cfe532c8a", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The unloading door was found not open.\r\nRest the unloading door relay.\r\nrun the complete cycle and confirmed the unit is working well.\r\nrest the unloading door relay due to a technical issue.", "created_at": "2025-07-22 07:08:36", "updated_at": "2025-07-22 07:08:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c0869dac-6d9c-420b-abcb-166457f2a3b3", "note_id": "214a07a1-9a0c-4f05-b7c8-889cfe532c8a", "original_filename": "230506_C.pdf", "stored_filename": "dcfb9074-3551-4212-b618-48a551d932b7.pdf", "file_path": "app/static/uploads/history/dcfb9074-3551-4212-b618-48a551d932b7.pdf", "mime_type": "application/pdf", "file_size": 530544, "upload_date": "2025-07-22 07:08:36"}]}, {"id": "bc3fcd51-4c96-44f2-be5b-898b26f9990c", "equipment_id": "D3S2-11617", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit.\r\nunit found that it's working properly.\r\nchecked the functions, and the unit is working well.", "created_at": "2025-07-22 07:12:13", "updated_at": "2025-07-22 07:12:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "39db27ff-3b8f-4457-9567-8906214e69cb", "note_id": "bc3fcd51-4c96-44f2-be5b-898b26f9990c", "original_filename": "D3S2-11617.pdf", "stored_filename": "5a69ace2-6dc1-418f-acf5-a2ba3aebaecc.pdf", "file_path": "app/static/uploads/history/5a69ace2-6dc1-418f-acf5-a2ba3aebaecc.pdf", "mime_type": "application/pdf", "file_size": 873760, "upload_date": "2025-07-22 07:12:13"}]}, {"id": "ea6e5405-0eea-4845-8613-86a8caa6e4f6", "equipment_id": "D3S2-10989", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and the functions.\r\nThe unit and function are working well.", "created_at": "2025-07-22 07:14:45", "updated_at": "2025-07-22 07:14:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "726190c1-1163-48aa-a957-d9cfab1a999a", "note_id": "ea6e5405-0eea-4845-8613-86a8caa6e4f6", "original_filename": "D3S2-10989.pdf", "stored_filename": "b191fa89-b4b7-40ba-9905-144eab7f857f.pdf", "file_path": "app/static/uploads/history/b191fa89-b4b7-40ba-9905-144eab7f857f.pdf", "mime_type": "application/pdf", "file_size": 873760, "upload_date": "2025-07-22 07:14:45"}]}, {"id": "dfc27d43-2296-42f2-80c5-f3e7f68e395a", "equipment_id": "5BM000271", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is inspected.\r\nfound new SPO2 sensors not working.\r\nChanged to another SPO2 sensor and tested; it's working.", "created_at": "2025-07-22 07:44:02", "updated_at": "2025-07-22 07:44:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e8de4e9e-0e64-4c69-8824-9b15e0155cb2", "note_id": "dfc27d43-2296-42f2-80c5-f3e7f68e395a", "original_filename": "05BM000271_B.pdf", "stored_filename": "6222d6d7-dede-44a1-93e1-b1f6731e40ac.pdf", "file_path": "app/static/uploads/history/6222d6d7-dede-44a1-93e1-b1f6731e40ac.pdf", "mime_type": "application/pdf", "file_size": 451600, "upload_date": "2025-07-22 07:44:02"}]}, {"id": "a88dceb3-f134-4056-9e32-0f22d3cec632", "equipment_id": "5BM000271", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The keypad membrane is repaired, and the NIBP button is tested.\r\nThe unit confirmed is working in good condition.", "created_at": "2025-07-22 07:51:11", "updated_at": "2025-07-22 07:51:11", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4c304e09-841b-4c22-9e60-f4a769f88345", "note_id": "a88dceb3-f134-4056-9e32-0f22d3cec632", "original_filename": "5BM000271_C.pdf", "stored_filename": "f3bd7a6e-ee67-4450-b736-aa5bd2a335e2.pdf", "file_path": "app/static/uploads/history/f3bd7a6e-ee67-4450-b736-aa5bd2a335e2.pdf", "mime_type": "application/pdf", "file_size": 451936, "upload_date": "2025-07-22 07:51:11"}]}, {"id": "36b9ebe9-1e4a-4ab7-bdf2-13d28cb5bdcd", "equipment_id": "101830001", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "machine checked with the manufacturer.\r\nThe machine needs a new sensor module.\r\nsensor module is tested from another machine to confirm.", "created_at": "2025-07-22 07:58:00", "updated_at": "2025-07-22 07:58:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8a8651dc-e009-4f41-9755-89bbac13e7ad", "note_id": "36b9ebe9-1e4a-4ab7-bdf2-13d28cb5bdcd", "original_filename": "101830001.pdf", "stored_filename": "1390fc22-f0a4-40d6-8fbf-0885293f7382.pdf", "file_path": "app/static/uploads/history/1390fc22-f0a4-40d6-8fbf-0885293f7382.pdf", "mime_type": "application/pdf", "file_size": 1383151, "upload_date": "2025-07-22 07:58:00"}]}, {"id": "682b64ae-e363-40e1-9ad8-31903c0aee9d", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Observed low output power on both ER YAG and ND YAG laser modules. \r\nLamp replacement is recommended for both.", "created_at": "2025-07-22 08:01:36", "updated_at": "2025-07-22 08:01:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "16b533e9-a1eb-4262-bf85-95267f9d0c2c", "note_id": "682b64ae-e363-40e1-9ad8-31903c0aee9d", "original_filename": "16100425-_b.pdf", "stored_filename": "be52c173-99f9-4d7d-b59c-ea35c47c99c7.pdf", "file_path": "app/static/uploads/history/be52c173-99f9-4d7d-b59c-ea35c47c99c7.pdf", "mime_type": "application/pdf", "file_size": 496512, "upload_date": "2025-07-22 08:01:36"}]}, {"id": "92701992-495a-4a1d-afd5-4628942f1159", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found the charger module faulty. \r\nReplaced with a standby charger module. \r\nOriginal module was taken to the workshop for repair and will be returned once servicing is complete.", "created_at": "2025-07-22 08:07:30", "updated_at": "2025-07-22 08:07:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8f5d12ac-005c-42df-a5f2-7e83b1ec059a", "note_id": "92701992-495a-4a1d-afd5-4628942f1159", "original_filename": "16100425-_c.pdf", "stored_filename": "436f0f64-99d3-4572-9d44-fec5fb6f17bb.pdf", "file_path": "app/static/uploads/history/436f0f64-99d3-4572-9d44-fec5fb6f17bb.pdf", "mime_type": "application/pdf", "file_size": 517536, "upload_date": "2025-07-22 08:07:30"}]}, {"id": "bf1572b5-84ba-4772-b163-ca8733e38e5b", "equipment_id": "2008-20022213-hash", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the shock delivered with the analyser, and it's working.\r\nPerformed hardware, front panel, and paddle interface tests.\r\nchecked and found that the unit is working well.", "created_at": "2025-07-22 08:10:33", "updated_at": "2025-07-22 08:10:33", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d6f224fe-c61c-4d2e-b515-859d17a0d38a", "note_id": "bf1572b5-84ba-4772-b163-ca8733e38e5b", "original_filename": "200820022213.pdf", "stored_filename": "e76e7fa6-2bea-4625-907a-d391ab08594f.pdf", "file_path": "app/static/uploads/history/e76e7fa6-2bea-4625-907a-d391ab08594f.pdf", "mime_type": "application/pdf", "file_size": 576096, "upload_date": "2025-07-22 08:10:33"}]}, {"id": "8fc8fcf1-b797-4d2c-8921-51c49d96e09c", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Detected Error 42. \r\nSuspected issue with the charger board unit. \r\nIssue must be resolved before checking the Internal components. \r\nOnce the issue is identified and resolved, we will provide an update ASAP.", "created_at": "2025-07-22 08:11:38", "updated_at": "2025-07-22 08:11:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "30c9f12d-94db-459f-a842-a7c110170c38", "note_id": "8fc8fcf1-b797-4d2c-8921-51c49d96e09c", "original_filename": "16100425-_d.pdf", "stored_filename": "327b315b-9ae9-4f89-8ddc-92dd3de1255b.pdf", "file_path": "app/static/uploads/history/327b315b-9ae9-4f89-8ddc-92dd3de1255b.pdf", "mime_type": "application/pdf", "file_size": 515040, "upload_date": "2025-07-22 08:11:39"}]}, {"id": "971e5c72-e723-4c87-b610-532dcbbad248", "equipment_id": "6020461067", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "delivered and replaced with a new oxygen cell, calibrated and tested.\r\nThe machine is working well and in good condition.", "created_at": "2025-07-22 08:15:07", "updated_at": "2025-07-22 08:15:07", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3e3eb1b4-ca0d-4193-a787-0a08199f4f3f", "note_id": "971e5c72-e723-4c87-b610-532dcbbad248", "original_filename": "6020461067.pdf", "stored_filename": "5983d3a4-290c-441f-a660-41b33ac86f69.pdf", "file_path": "app/static/uploads/history/5983d3a4-290c-441f-a660-41b33ac86f69.pdf", "mime_type": "application/pdf", "file_size": 393584, "upload_date": "2025-07-22 08:15:07"}]}, {"id": "506bc6ac-41dc-4674-afb9-cde54312fbc6", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Updated CAVI and SYS software. \r\nAll connections tested. \r\nSystem is functioning properly.", "created_at": "2025-07-22 08:16:27", "updated_at": "2025-07-22 08:16:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "cea152cf-dabb-4605-a30d-f2d32dff519b", "note_id": "506bc6ac-41dc-4674-afb9-cde54312fbc6", "original_filename": "16100425-_e.pdf", "stored_filename": "b867ecc5-6103-4287-95cb-44c03227bd75.pdf", "file_path": "app/static/uploads/history/b867ecc5-6103-4287-95cb-44c03227bd75.pdf", "mime_type": "application/pdf", "file_size": 498768, "upload_date": "2025-07-22 08:16:27"}]}, {"id": "94fa773d-43b3-4d29-b5be-f0b0bdd9e1b3", "equipment_id": "414741", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is inspected & tested.\r\nThe unit is working well with backup.", "created_at": "2025-07-22 08:19:09", "updated_at": "2025-07-22 08:19:09", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "6782c820-5580-4479-9f0a-58abf0d74271", "note_id": "94fa773d-43b3-4d29-b5be-f0b0bdd9e1b3", "original_filename": "699RP00-_00414741.pdf", "stored_filename": "a4ee9e91-3157-4696-bdaa-b75edd281b26.pdf", "file_path": "app/static/uploads/history/a4ee9e91-3157-4696-bdaa-b75edd281b26.pdf", "mime_type": "application/pdf", "file_size": 495392, "upload_date": "2025-07-22 08:19:09"}]}, {"id": "d8ada3dc-0a25-44e2-96b6-962c01baefbf", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the pump connector with a new one. \r\nReconnected the tubing to its original configuration. \r\nTested for leakage and refilled with distilled water—no leakage observed. \r\nUnit checked and confirmed to be working properly.", "created_at": "2025-07-22 08:21:40", "updated_at": "2025-07-22 08:21:41", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1e4aeab1-12c3-4993-8418-697d4a4092c1", "note_id": "d8ada3dc-0a25-44e2-96b6-962c01baefbf", "original_filename": "16100425-_f.pdf", "stored_filename": "0fe9fd6f-35f1-408f-9342-8f434b01c822.pdf", "file_path": "app/static/uploads/history/0fe9fd6f-35f1-408f-9342-8f434b01c822.pdf", "mime_type": "application/pdf", "file_size": 551568, "upload_date": "2025-07-22 08:21:41"}]}, {"id": "43068a79-c31a-48b0-a763-eab739246be4", "equipment_id": "261436-EMI9CO", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit is inspected.\r\ninterchanged BP cuffs.\r\nunit is working properly.", "created_at": "2025-07-22 08:22:28", "updated_at": "2025-07-22 08:22:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "087d8974-9d16-4bf7-8a8f-1594038fb232", "note_id": "43068a79-c31a-48b0-a763-eab739246be4", "original_filename": "261436-EMI9CO.pdf", "stored_filename": "84bdc7d1-bc59-49af-ac6e-8364c2dd655b.pdf", "file_path": "app/static/uploads/history/84bdc7d1-bc59-49af-ac6e-8364c2dd655b.pdf", "mime_type": "application/pdf", "file_size": 488880, "upload_date": "2025-07-22 08:22:28"}]}, {"id": "fcef8012-77ef-4f19-b8f8-3ec46c0f2ba5", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Detected water leakage from the bottom pump area. \r\nUpon inspection, the output nozzle connector was found broken and requires replacement. The unit is currently non-operational.", "created_at": "2025-07-22 08:26:45", "updated_at": "2025-07-22 08:26:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2cf142ba-2830-4af8-93e7-65bd8e247601", "note_id": "fcef8012-77ef-4f19-b8f8-3ec46c0f2ba5", "original_filename": "16100425-_g.pdf", "stored_filename": "f6b19ed8-5ec1-4ef5-873a-1a765a10630e.pdf", "file_path": "app/static/uploads/history/f6b19ed8-5ec1-4ef5-873a-1a765a10630e.pdf", "mime_type": "application/pdf", "file_size": 519888, "upload_date": "2025-07-22 08:26:46"}]}, {"id": "45de9323-bd24-4ccf-b290-715beb72dbaf", "equipment_id": "551101299960NB300047", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "found that the inversion module and batteries are faulty.\r\nneed to change the UPS.", "created_at": "2025-07-22 08:27:04", "updated_at": "2025-07-22 08:27:04", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5ec59256-fe2d-4b7f-af85-559ab31d160c", "note_id": "45de9323-bd24-4ccf-b290-715beb72dbaf", "original_filename": "log_no._1930.pdf", "stored_filename": "b2de5902-59b6-4ba6-bec5-2a120864a1a4.pdf", "file_path": "app/static/uploads/history/b2de5902-59b6-4ba6-bec5-2a120864a1a4.pdf", "mime_type": "application/pdf", "file_size": 500224, "upload_date": "2025-07-22 08:27:04"}]}, {"id": "7adb32f1-ebae-4300-a0f2-2488707d152f", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "BG & ISE MC cleaned.\r\nISE MC deactivated.\r\nInstrument type set to b221<2>\r\nCalibration & QC done.\r\nAll 3 levels are okay.", "created_at": "2025-07-22 08:31:53", "updated_at": "2025-07-22 08:31:53", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b0ef0fc7-120d-46ee-887a-8c5a22d950cb", "note_id": "7adb32f1-ebae-4300-a0f2-2488707d152f", "original_filename": "19239.pdf", "stored_filename": "6b6bacb0-8eb6-484e-82f2-d05bef726162.pdf", "file_path": "app/static/uploads/history/6b6bacb0-8eb6-484e-82f2-d05bef726162.pdf", "mime_type": "application/pdf", "file_size": 978863, "upload_date": "2025-07-22 08:31:53"}]}, {"id": "b71109b5-1563-48f2-ab8b-695f18d50354", "equipment_id": "9914-0300-3443", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Installed new dye kit into the laser unit. \r\nPerformed system energy and circuit calibration. \r\nSystem checked and verified—laser is functioning properly.", "created_at": "2025-07-22 08:32:05", "updated_at": "2025-07-22 08:32:05", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e9adf359-7295-4604-bea5-b96883b808ed", "note_id": "b71109b5-1563-48f2-ab8b-695f18d50354", "original_filename": "9914-0300-3443.pdf", "stored_filename": "f4d014d3-52a5-45a8-a350-f5365726d584.pdf", "file_path": "app/static/uploads/history/f4d014d3-52a5-45a8-a350-f5365726d584.pdf", "mime_type": "application/pdf", "file_size": 458800, "upload_date": "2025-07-22 08:32:05"}]}, {"id": "00eae901-bbd4-4330-89cb-626405e68b7b", "equipment_id": "EM09311018", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced touchscreen with a new one. \r\nPerformed testing and calibration. \r\nUnit is now fully operational.", "created_at": "2025-07-22 08:36:35", "updated_at": "2025-07-22 08:36:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "619aacee-70e0-4534-b312-b0934507d65f", "note_id": "00eae901-bbd4-4330-89cb-626405e68b7b", "original_filename": "EM09311018.pdf", "stored_filename": "3a5f4245-5bfb-4024-be82-ad6a5fa7bddc.pdf", "file_path": "app/static/uploads/history/3a5f4245-5bfb-4024-be82-ad6a5fa7bddc.pdf", "mime_type": "application/pdf", "file_size": 511856, "upload_date": "2025-07-22 08:36:35"}]}, {"id": "e1096321-5f01-4662-a890-07505ff8f2bd", "equipment_id": "EM09311018", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Touchscreen found to be malfunctioning; sensitivity is inconsistent and not responding properly. \r\nManufacturer will be consulted for further evaluation and update will follow ASAP.", "created_at": "2025-07-22 08:40:49", "updated_at": "2025-07-22 08:40:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d243abbe-9151-4b3e-a80e-226b3308bd72", "note_id": "e1096321-5f01-4662-a890-07505ff8f2bd", "original_filename": "EM09311018-_b.pdf", "stored_filename": "1bed0951-4bbd-403b-b8e2-109bf8fffefc.pdf", "file_path": "app/static/uploads/history/1bed0951-4bbd-403b-b8e2-109bf8fffefc.pdf", "mime_type": "application/pdf", "file_size": 450192, "upload_date": "2025-07-22 08:40:49"}]}, {"id": "d4b97a4a-d808-41b7-bf3b-87c84d17c86c", "equipment_id": "T7296", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked and found that the cable inside the machine for the handswitch is damaged.\r\nrepaired the damage and is working properly.", "created_at": "2025-07-22 08:41:34", "updated_at": "2025-07-22 08:41:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2523738a-6d49-41cc-91cc-d2cdd61b9399", "note_id": "d4b97a4a-d808-41b7-bf3b-87c84d17c86c", "original_filename": "T7296.pdf", "stored_filename": "ba6eb2ca-18e5-454a-b48d-a2d59638f8c0.pdf", "file_path": "app/static/uploads/history/ba6eb2ca-18e5-454a-b48d-a2d59638f8c0.pdf", "mime_type": "application/pdf", "file_size": 573312, "upload_date": "2025-07-22 08:41:34"}]}, {"id": "2d3c2282-5191-4d4a-b7a3-21f53df8c4b7", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Checked FMS tubing set.\r\nunit is okay.", "created_at": "2025-07-22 08:43:44", "updated_at": "2025-07-22 08:43:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d6cd5ced-68ae-408c-9fdb-382d90ddb0c7", "note_id": "2d3c2282-5191-4d4a-b7a3-21f53df8c4b7", "original_filename": "19239_B.pdf", "stored_filename": "094bad6f-1767-403c-a9f0-e0659b374ad6.pdf", "file_path": "app/static/uploads/history/094bad6f-1767-403c-a9f0-e0659b374ad6.pdf", "mime_type": "application/pdf", "file_size": 313712, "upload_date": "2025-07-22 08:43:44"}]}, {"id": "96d0f63c-a9e0-4517-bf23-854b706d16fa", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Shifted and reinstalled two (2) ENT workstation units and audiometer booth to the 5th floor. All functions tested and found to be working properly.", "created_at": "2025-07-22 08:46:47", "updated_at": "2025-07-22 08:46:47", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1535689c-019d-45db-b3e7-4a155a085206", "note_id": "96d0f63c-a9e0-4517-bf23-854b706d16fa", "original_filename": "111632045-_f.pdf", "stored_filename": "6505b432-b7af-47a1-aabf-62ec5cbd06f6.pdf", "file_path": "app/static/uploads/history/6505b432-b7af-47a1-aabf-62ec5cbd06f6.pdf", "mime_type": "application/pdf", "file_size": 475680, "upload_date": "2025-07-22 08:46:47"}]}, {"id": "1ca83d93-6d51-4ddb-b1ee-1cf67ca4165d", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "After checking the BG SD cartridge fan, it was not moving.\r\ncleaned all the parts required.\r\ncleaned and lubricated the SD fan and found BG SD heating was crossed.\r\nRun calibration and QC.\r\nperformed all the service required.\r\nReset the instrument.\r\nall the functions working.", "created_at": "2025-07-22 08:47:41", "updated_at": "2025-07-22 08:47:41", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "73ab0f9e-c8ea-4f8e-8007-4b790d583e34", "note_id": "1ca83d93-6d51-4ddb-b1ee-1cf67ca4165d", "original_filename": "19239_C.pdf", "stored_filename": "368ba608-0b0c-44e2-bb65-1571b010799a.pdf", "file_path": "app/static/uploads/history/368ba608-0b0c-44e2-bb65-1571b010799a.pdf", "mime_type": "application/pdf", "file_size": 666112, "upload_date": "2025-07-22 08:47:41"}]}, {"id": "98de331e-1e91-4530-9463-107f74e2e7a3", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Rinse bottle empty.\r\nnew rinse bottled installed.\r\nRinse okay.\r\nCalibration and QC were done and passed.", "created_at": "2025-07-22 08:48:59", "updated_at": "2025-07-22 08:48:59", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0938b04e-d863-4afb-a46a-e97469cd6dc0", "note_id": "98de331e-1e91-4530-9463-107f74e2e7a3", "original_filename": "19239_D.pdf", "stored_filename": "cc583e77-5241-48b4-a0d3-686e642e8a22.pdf", "file_path": "app/static/uploads/history/cc583e77-5241-48b4-a0d3-686e642e8a22.pdf", "mime_type": "application/pdf", "file_size": 391646, "upload_date": "2025-07-22 08:48:59"}]}, {"id": "79b0399b-3615-4edb-a5b8-962c159a25e8", "equipment_id": "N-A-3", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Nurse Panel checking.", "created_at": "2025-07-22 08:50:58", "updated_at": "2025-07-22 08:50:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8e64b024-1a35-41c0-b5fd-7f02768282b1", "note_id": "79b0399b-3615-4edb-a5b8-962c159a25e8", "original_filename": "N-A_3.pdf", "stored_filename": "5884ccda-6ad2-40b7-9fc7-03cc1b60542c.pdf", "file_path": "app/static/uploads/history/5884ccda-6ad2-40b7-9fc7-03cc1b60542c.pdf", "mime_type": "application/pdf", "file_size": 508112, "upload_date": "2025-07-22 08:50:58"}]}, {"id": "017ed717-c4b3-49d5-b546-aad0931180be", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "ISE is in use, and it was deactivated.\r\nunit is reactivated, causing a temp alarm. ISE parameters were deactivated again, and the machine rebooted and is working fine.", "created_at": "2025-07-22 08:53:00", "updated_at": "2025-07-22 08:53:01", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "10325fd8-d8d2-4554-9954-f2d5d67e9e0c", "note_id": "017ed717-c4b3-49d5-b546-aad0931180be", "original_filename": "19239_E.pdf", "stored_filename": "8171f779-8b0a-4c2c-b4ae-55593a148875.pdf", "file_path": "app/static/uploads/history/8171f779-8b0a-4c2c-b4ae-55593a148875.pdf", "mime_type": "application/pdf", "file_size": 577630, "upload_date": "2025-07-22 08:53:01"}]}, {"id": "4a3d3a98-2362-463a-9c20-a7460adb4719", "equipment_id": "HDHK51647", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replace the oxygen sensor with a new one for units of the incubator. \r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 08:57:33", "updated_at": "2025-07-22 08:57:33", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c67facbc-86fb-4155-a2e5-587cfd94b2a6", "note_id": "4a3d3a98-2362-463a-9c20-a7460adb4719", "original_filename": "HDHK51647_B.pdf", "stored_filename": "7097a784-d058-4e16-9229-94a553c8afed.pdf", "file_path": "app/static/uploads/history/7097a784-d058-4e16-9229-94a553c8afed.pdf", "mime_type": "application/pdf", "file_size": 920718, "upload_date": "2025-07-22 08:57:33"}]}, {"id": "ebe289c2-1157-441f-86ce-7bb0ac9c3c4a", "equipment_id": "460016-M2261931004", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified faulty cable and replaced it with a new one. \r\nAll functions checked and confirmed to be working properly.", "created_at": "2025-07-22 08:58:16", "updated_at": "2025-07-22 08:58:16", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "15c23f22-0ec9-4e5a-8b52-50b6c6b08e18", "note_id": "ebe289c2-1157-441f-86ce-7bb0ac9c3c4a", "original_filename": "LOG__2539.pdf", "stored_filename": "b45647a8-be62-4c25-aa0b-3425934ddd55.pdf", "file_path": "app/static/uploads/history/b45647a8-be62-4c25-aa0b-3425934ddd55.pdf", "mime_type": "application/pdf", "file_size": 502816, "upload_date": "2025-07-22 08:58:16"}]}, {"id": "01a82e98-ad4f-40d3-a238-1533facb5a1f", "equipment_id": "HDHN50630", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the oxygen sensor for the units of the incubator.\r\nchecked and confirmed that the two units are working properly.", "created_at": "2025-07-22 08:59:54", "updated_at": "2025-07-22 08:59:54", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "706b32a1-1d6f-4699-86fa-f619a9326a91", "note_id": "01a82e98-ad4f-40d3-a238-1533facb5a1f", "original_filename": "HDHN50630_B.pdf", "stored_filename": "61ced6a8-de7f-4f4d-b001-5a6df0a44f44.pdf", "file_path": "app/static/uploads/history/61ced6a8-de7f-4f4d-b001-5a6df0a44f44.pdf", "mime_type": "application/pdf", "file_size": 920718, "upload_date": "2025-07-22 08:59:54"}]}, {"id": "484b361c-9b96-4dcd-ba67-2e43a7f31990", "equipment_id": "311072-M1666", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced broken printer cover with a new one.\r\nFunctionality tested and confirmed to be working properly.", "created_at": "2025-07-22 09:03:13", "updated_at": "2025-07-22 09:03:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1bfc3d94-d090-4cf4-9f33-4214dc460052", "note_id": "484b361c-9b96-4dcd-ba67-2e43a7f31990", "original_filename": "LOG__2509.pdf", "stored_filename": "ffe053d3-da0f-4fa3-85de-506073ce0f09.pdf", "file_path": "app/static/uploads/history/ffe053d3-da0f-4fa3-85de-506073ce0f09.pdf", "mime_type": "application/pdf", "file_size": 488256, "upload_date": "2025-07-22 09:03:13"}]}, {"id": "5e0ecef5-576e-44a6-94f2-b857e284507e", "equipment_id": "1010-2-203", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "preventive maintenance routine.\r\nCheck the unit.\r\nactivation of the system\r\nCheck the energy and gas system.\r\nCheck functions and operations.\r\nThe system tested is working well.", "created_at": "2025-07-22 09:05:27", "updated_at": "2025-07-22 09:05:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0b2b41e9-6308-4223-97b1-afa6c57cdd62", "note_id": "5e0ecef5-576e-44a6-94f2-b857e284507e", "original_filename": "1010-2-203.pdf", "stored_filename": "a5c65bf8-81b9-4313-9c44-804f519aace0.pdf", "file_path": "app/static/uploads/history/a5c65bf8-81b9-4313-9c44-804f519aace0.pdf", "mime_type": "application/pdf", "file_size": 439424, "upload_date": "2025-07-22 09:05:27"}]}, {"id": "fa05f16c-2584-4381-aa01-1493053c81ad", "equipment_id": "460016", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Printer cover found broken and requires replacement.", "created_at": "2025-07-22 09:08:19", "updated_at": "2025-07-22 09:08:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a47396dd-eca6-4da5-bfd2-fd047d39ca6a", "note_id": "fa05f16c-2584-4381-aa01-1493053c81ad", "original_filename": "LOG__1552.pdf", "stored_filename": "fd5590ea-154d-4029-a7f1-348eaddd666d.pdf", "file_path": "app/static/uploads/history/fd5590ea-154d-4029-a7f1-348eaddd666d.pdf", "mime_type": "application/pdf", "file_size": 423184, "upload_date": "2025-07-22 09:08:19"}]}, {"id": "83b65cfc-ad3a-471a-8c30-01a4147c08f1", "equipment_id": "N-A-62", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Bedside rail locking system found broken. \r\nFaulty part removed and taken for inspection and evaluation.", "created_at": "2025-07-22 09:12:42", "updated_at": "2025-07-22 09:12:42", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "28950578-8ebd-4f50-af8e-326d2d870378", "note_id": "83b65cfc-ad3a-471a-8c30-01a4147c08f1", "original_filename": "LOG__1502.pdf", "stored_filename": "f119bd5a-9791-4061-8d66-d01c3e36cc0c.pdf", "file_path": "app/static/uploads/history/f119bd5a-9791-4061-8d66-d01c3e36cc0c.pdf", "mime_type": "application/pdf", "file_size": 492032, "upload_date": "2025-07-22 09:12:42"}]}, {"id": "e9746182-6baa-4578-aac9-13fa27d343f4", "equipment_id": "260597-M19711210001", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced the main board with a new one.\r\nchecked and confirmed that the XMI module is working properly.", "created_at": "2025-07-22 09:12:48", "updated_at": "2025-07-22 09:12:48", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "44852c52-efc0-48dc-9a42-d962a625e899", "note_id": "e9746182-6baa-4578-aac9-13fa27d343f4", "original_filename": "M19711220001.pdf", "stored_filename": "e4e093c8-5023-4aa1-8a8e-cb9444c001a3.pdf", "file_path": "app/static/uploads/history/e4e093c8-5023-4aa1-8a8e-cb9444c001a3.pdf", "mime_type": "application/pdf", "file_size": 491568, "upload_date": "2025-07-22 09:12:48"}]}, {"id": "3167a6cf-483c-4f64-8a8c-c12d0f170f75", "equipment_id": "460016EM22CI93", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "ECG machine V2 port was not tracing due to a faulty cable. \r\nReplaced the cable, tested the unit, and confirmed it is working properly.", "created_at": "2025-07-22 09:15:48", "updated_at": "2025-07-22 09:15:48", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c0c093d4-5c0c-4d1c-a2c7-718f1f2b392c", "note_id": "3167a6cf-483c-4f64-8a8c-c12d0f170f75", "original_filename": "LOG__1511.pdf", "stored_filename": "d9aec797-2d56-4a4e-a6a7-a2c406cd3626.pdf", "file_path": "app/static/uploads/history/d9aec797-2d56-4a4e-a6a7-a2c406cd3626.pdf", "mime_type": "application/pdf", "file_size": 436160, "upload_date": "2025-07-22 09:15:48"}]}, {"id": "fa1886b3-e14c-4337-8d3e-c8882419d60e", "equipment_id": "205009259", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Transferred the Combi Device from Maha Clinic to Alorf Hospital.", "created_at": "2025-07-22 09:20:23", "updated_at": "2025-07-22 09:20:23", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "dae6c214-7eef-459f-a5c2-f3101d047329", "note_id": "fa1886b3-e14c-4337-8d3e-c8882419d60e", "original_filename": "LOG_1069.pdf", "stored_filename": "67d20023-3dd0-4373-ae86-40ae95e51d13.pdf", "file_path": "app/static/uploads/history/67d20023-3dd0-4373-ae86-40ae95e51d13.pdf", "mime_type": "application/pdf", "file_size": 520672, "upload_date": "2025-07-22 09:20:23"}]}, {"id": "5a59b393-64ca-4619-9b2a-e3db1c87a013", "equipment_id": "2632794", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "<PERSON>rest (up/down) was not functioning.\r\nIssue resolved and full movement restored. \r\nUnit is now operating properly", "created_at": "2025-07-22 09:26:13", "updated_at": "2025-07-22 09:26:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "44e55feb-df5f-43f2-8e46-50f290ccf1c3", "note_id": "5a59b393-64ca-4619-9b2a-e3db1c87a013", "original_filename": "2632794-_b.pdf", "stored_filename": "ee678964-4dca-4518-88a1-2de6eb64dff7.pdf", "file_path": "app/static/uploads/history/ee678964-4dca-4518-88a1-2de6eb64dff7.pdf", "mime_type": "application/pdf", "file_size": 1575265, "upload_date": "2025-07-22 09:26:13"}]}, {"id": "24892604-dfef-4e8a-b0e4-310df3e2eb79", "equipment_id": "NV111020091", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "An oil leak was found from the base panel.\r\nClean the base panel and tubing.", "created_at": "2025-07-22 09:36:28", "updated_at": "2025-07-22 09:36:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f03e9faa-9f78-4971-ae86-ea5084b222b3", "note_id": "24892604-dfef-4e8a-b0e4-310df3e2eb79", "original_filename": "NV111020091.pdf", "stored_filename": "6dba8331-ea9d-499d-98d9-11344b1081a5.pdf", "file_path": "app/static/uploads/history/6dba8331-ea9d-499d-98d9-11344b1081a5.pdf", "mime_type": "application/pdf", "file_size": 482976, "upload_date": "2025-07-22 09:36:28"}]}, {"id": "736be461-40e3-4427-b5e9-fbf681808ebe", "equipment_id": "NV111020091", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checking the unit and found leaking from the cylinder tube.\r\nfixed the leaking and refilled the oil.\r\nAll connections tested okay.\r\nsystem working well.", "created_at": "2025-07-22 09:38:29", "updated_at": "2025-07-22 09:38:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "81396ca4-664a-4496-afb5-ecb1fffa0d8f", "note_id": "736be461-40e3-4427-b5e9-fbf681808ebe", "original_filename": "NV111020091_B.pdf", "stored_filename": "9efd4e32-5bea-4b75-b971-0a78f41a88cf.pdf", "file_path": "app/static/uploads/history/9efd4e32-5bea-4b75-b971-0a78f41a88cf.pdf", "mime_type": "application/pdf", "file_size": 504720, "upload_date": "2025-07-22 09:38:30"}]}, {"id": "621e10c6-7d99-4b5c-a446-814d4a1f39e8", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced leaking irrigation handpiece (entire unit) with a new one. \r\nTested and confirmed fully functional.", "created_at": "2025-07-22 09:40:28", "updated_at": "2025-07-22 09:40:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "81ea40a3-8cbd-4411-9a22-beb1b5176672", "note_id": "621e10c6-7d99-4b5c-a446-814d4a1f39e8", "original_filename": "111632045-_g.pdf", "stored_filename": "8ceac7ec-d752-4c7e-867b-88fc91241e37.pdf", "file_path": "app/static/uploads/history/8ceac7ec-d752-4c7e-867b-88fc91241e37.pdf", "mime_type": "application/pdf", "file_size": 508976, "upload_date": "2025-07-22 09:40:28"}]}, {"id": "56063b0d-21c3-4e6b-a6a8-b6e0ee0e6829", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found that the Enthermo was tripped off. \r\nRepaired and reset the Enthermo connected to the irrigation handpiece. \r\nTested—no leakage observed. Unit kept under observation for monitoring.", "created_at": "2025-07-22 09:40:57", "updated_at": "2025-07-22 09:40:57", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "721ab0dc-6608-4204-a229-ccb472aac8a5", "note_id": "56063b0d-21c3-4e6b-a6a8-b6e0ee0e6829", "original_filename": "111632045-_h.pdf", "stored_filename": "51375ed5-e32b-455a-914e-15ba402cb1ba.pdf", "file_path": "app/static/uploads/history/51375ed5-e32b-455a-914e-15ba402cb1ba.pdf", "mime_type": "application/pdf", "file_size": 520944, "upload_date": "2025-07-22 09:40:57"}]}, {"id": "2e98d243-5ed2-4ace-b3af-7eb29b1f5045", "equipment_id": "121ABDR0033", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced the camera head with a new one.\r\ntested and confirmed the unit is working okay.\r\ndemo given to staff.", "created_at": "2025-07-22 09:44:13", "updated_at": "2025-07-22 09:44:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "31ece9fb-1bb5-43e8-9665-336529ffef77", "note_id": "2e98d243-5ed2-4ace-b3af-7eb29b1f5045", "original_filename": "121ABDR0033.pdf", "stored_filename": "28829eb6-f7cf-40f0-a30f-01328dc10bf5.pdf", "file_path": "app/static/uploads/history/28829eb6-f7cf-40f0-a30f-01328dc10bf5.pdf", "mime_type": "application/pdf", "file_size": 538608, "upload_date": "2025-07-22 09:44:13"}]}, {"id": "39484fbe-95e6-40c4-bfd7-7b49fa9032f3", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the water filter inside the workstation with a new one. \r\nRepaired and cleaned the Enthermo unit. \r\nUnit fixed and tested—now fully operational.", "created_at": "2025-07-22 09:44:26", "updated_at": "2025-07-22 09:44:26", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8c7c060a-e11f-4188-8f6f-925d93c67db6", "note_id": "39484fbe-95e6-40c4-bfd7-7b49fa9032f3", "original_filename": "111632045-_i.pdf", "stored_filename": "eac4a0bd-637d-45a6-9520-15be91229882.pdf", "file_path": "app/static/uploads/history/eac4a0bd-637d-45a6-9520-15be91229882.pdf", "mime_type": "application/pdf", "file_size": 524560, "upload_date": "2025-07-22 09:44:26"}]}, {"id": "cbd81f7e-7bb7-4dc2-bf6f-62a46bbdaf17", "equipment_id": "AMXH00507", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replacement of power supply.\r\ncalibration and cleaning of power supply.", "created_at": "2025-07-22 09:46:08", "updated_at": "2025-07-22 09:46:08", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bfbfcda8-4e1f-4f49-b216-12b4f74c2e5f", "note_id": "cbd81f7e-7bb7-4dc2-bf6f-62a46bbdaf17", "original_filename": "AMXH00507.pdf", "stored_filename": "56f0c6a9-479c-4948-a7f5-a7a6eb19f46c.pdf", "file_path": "app/static/uploads/history/56f0c6a9-479c-4948-a7f5-a7a6eb19f46c.pdf", "mime_type": "application/pdf", "file_size": 137921, "upload_date": "2025-07-22 09:46:08"}]}, {"id": "725fa3d4-c7f8-4a4d-9f2e-a42083c9a9d1", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Fully cleaned the Enthermo unit, including water tubes and drain system. \r\nTested and confirmed to be working well.", "created_at": "2025-07-22 09:46:59", "updated_at": "2025-07-22 09:46:59", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "015aaabc-5864-42e2-a037-d0dfd26bcb1a", "note_id": "725fa3d4-c7f8-4a4d-9f2e-a42083c9a9d1", "original_filename": "111632045-_j.pdf", "stored_filename": "dc7e834d-cd9b-4ac5-a241-646a0cd10060.pdf", "file_path": "app/static/uploads/history/dc7e834d-cd9b-4ac5-a241-646a0cd10060.pdf", "mime_type": "application/pdf", "file_size": 502512, "upload_date": "2025-07-22 09:46:59"}]}, {"id": "c4f9de56-16de-4755-b1b7-5c3adfedd3cd", "equipment_id": "1010-2-203", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance done.\r\nattending an operation with the doctor.\r\ninstallation of the new Alorf gas tank.\r\nWork completed.", "created_at": "2025-07-22 09:49:01", "updated_at": "2025-07-22 09:49:01", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5c0b9f73-5040-45d4-be24-7a0addfe915d", "note_id": "c4f9de56-16de-4755-b1b7-5c3adfedd3cd", "original_filename": "1010-2-203_B.pdf", "stored_filename": "59c3a9fa-646a-411d-b42e-7fc842fa3966.pdf", "file_path": "app/static/uploads/history/59c3a9fa-646a-411d-b42e-7fc842fa3966.pdf", "mime_type": "application/pdf", "file_size": 445280, "upload_date": "2025-07-22 09:49:01"}]}, {"id": "cc852faf-7160-4d42-81ad-584a2cbd50cf", "equipment_id": "103732017", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Detected water leakage from the suction jar side. \r\nIssue repaired and leak resolved. \r\nUnit tested and confirmed to be functioning properly.", "created_at": "2025-07-22 09:54:28", "updated_at": "2025-07-22 09:54:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "038607fb-a2df-4d6d-981b-5007c671f220", "note_id": "cc852faf-7160-4d42-81ad-584a2cbd50cf", "original_filename": "103732017-_c.pdf", "stored_filename": "1c530ef1-7b4f-4d9e-bdd2-eb9b02a05263.pdf", "file_path": "app/static/uploads/history/1c530ef1-7b4f-4d9e-bdd2-eb9b02a05263.pdf", "mime_type": "application/pdf", "file_size": 507024, "upload_date": "2025-07-22 09:54:28"}]}, {"id": "75b79ac5-adb9-47ca-96bc-38eb50f0184c", "equipment_id": "208", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found the neutral connection is not detecting.\r\nCheck the connection and replace the connector and check the board.\r\nCurrently the unit is not working.", "created_at": "2025-07-22 09:54:41", "updated_at": "2025-07-22 09:54:41", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "633a56c8-aed6-4341-af6f-b5a3cea256e2", "note_id": "75b79ac5-adb9-47ca-96bc-38eb50f0184c", "original_filename": "208.pdf", "stored_filename": "25122575-f275-4cf3-80b3-343d577e62a3.pdf", "file_path": "app/static/uploads/history/25122575-f275-4cf3-80b3-343d577e62a3.pdf", "mime_type": "application/pdf", "file_size": 522528, "upload_date": "2025-07-22 09:54:41"}]}, {"id": "bc09cb04-de24-4f2b-affc-55b2b9e78903", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Detected water leakage from the handpiece. \r\nReplaced the handpiece connector and resolved the leak. \r\nAll connectors tested and confirmed to be functioning properly.", "created_at": "2025-07-22 14:00:31", "updated_at": "2025-07-22 14:00:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ea5c9058-98a8-42b1-9528-277faec44375", "note_id": "bc09cb04-de24-4f2b-affc-55b2b9e78903", "original_filename": "111632045-_k.pdf", "stored_filename": "25001ccd-f8d5-43af-bf9c-823cc8ba0599.pdf", "file_path": "app/static/uploads/history/25001ccd-f8d5-43af-bf9c-823cc8ba0599.pdf", "mime_type": "application/pdf", "file_size": 497344, "upload_date": "2025-07-22 14:00:31"}]}, {"id": "9c0e527f-49e5-412d-8374-be2e194bca49", "equipment_id": "IMIDD17203836", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Calibration done.", "created_at": "2025-07-22 14:02:17", "updated_at": "2025-07-22 14:02:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "072c3be4-4775-4724-8158-591a8d5c7caf", "note_id": "9c0e527f-49e5-412d-8374-be2e194bca49", "original_filename": "IMIDD1703836.pdf", "stored_filename": "9470f6c9-1e52-4ae5-97a1-6bdba8b0a7c1.pdf", "file_path": "app/static/uploads/history/9470f6c9-1e52-4ae5-97a1-6bdba8b0a7c1.pdf", "mime_type": "application/pdf", "file_size": 498240, "upload_date": "2025-07-22 14:02:18"}]}, {"id": "8ebb99f2-bf3a-478d-9367-2d37c2d15c4b", "equipment_id": "36410", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Suction motor has been replaced. \r\nThe machine is now operating according to specifications.", "created_at": "2025-07-22 14:11:26", "updated_at": "2025-07-22 14:11:26", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "dab97630-38ce-4bb3-8f07-f45ce736c493", "note_id": "8ebb99f2-bf3a-478d-9367-2d37c2d15c4b", "original_filename": "36410.pdf", "stored_filename": "d929d77c-da78-422a-8ac3-9528f6be4ca5.pdf", "file_path": "app/static/uploads/history/d929d77c-da78-422a-8ac3-9528f6be4ca5.pdf", "mime_type": "application/pdf", "file_size": 515888, "upload_date": "2025-07-22 14:11:26"}]}, {"id": "f65a9b41-263b-4239-af6c-59caac6878c9", "equipment_id": "36410", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The unit has been repaired, to resolve the suction issue. It is now functioning properly.", "created_at": "2025-07-22 14:15:00", "updated_at": "2025-07-22 14:15:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "469106a0-d321-4d93-b1bd-643e82d6ae21", "note_id": "f65a9b41-263b-4239-af6c-59caac6878c9", "original_filename": "36410-_b.pdf", "stored_filename": "9ed38316-0946-45e3-9215-1b1a6f493796.pdf", "file_path": "app/static/uploads/history/9ed38316-0946-45e3-9215-1b1a6f493796.pdf", "mime_type": "application/pdf", "file_size": 1075042, "upload_date": "2025-07-22 14:15:00"}]}, {"id": "edbbd160-b1c3-4d1e-b5b1-e5bc3c524e8b", "equipment_id": "36410", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The unit has been repaired. \r\nA spare part (valve) was replaced to restore proper functionality.", "created_at": "2025-07-22 14:17:13", "updated_at": "2025-07-22 14:17:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ba9a4063-3fa9-4230-ac1c-63b0e61a494f", "note_id": "edbbd160-b1c3-4d1e-b5b1-e5bc3c524e8b", "original_filename": "36410-_c.pdf", "stored_filename": "cb28b382-d592-4f09-8e96-8efa371b2344.pdf", "file_path": "app/static/uploads/history/cb28b382-d592-4f09-8e96-8efa371b2344.pdf", "mime_type": "application/pdf", "file_size": 453136, "upload_date": "2025-07-22 14:17:13"}]}, {"id": "a5781136-76c1-4f64-9c4e-07f786001705", "equipment_id": "121ABDR0033", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Fixed the manual zoom issues.\r\nReset the system.\r\nAll connections are tested okay.\r\nsystem is working well.", "created_at": "2025-07-22 14:19:16", "updated_at": "2025-07-22 14:19:16", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "800a6f73-7b51-4262-88b2-935531ca390b", "note_id": "a5781136-76c1-4f64-9c4e-07f786001705", "original_filename": "121ABDR0033_B.pdf", "stored_filename": "d8de61d0-4d73-4e42-8ef2-574970b25b8c.pdf", "file_path": "app/static/uploads/history/d8de61d0-4d73-4e42-8ef2-574970b25b8c.pdf", "mime_type": "application/pdf", "file_size": 489616, "upload_date": "2025-07-22 14:19:16"}]}, {"id": "8358ce28-faae-4cf3-a643-7a79ac353ace", "equipment_id": "103732017", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The drainage pipe and water connection of the ENT workstation have been fixed.\r\nThe unit has been tested and confirmed to be working properly.", "created_at": "2025-07-22 14:19:49", "updated_at": "2025-07-22 14:19:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8b77371b-683f-49fb-ae67-ba6ed7b90437", "note_id": "8358ce28-faae-4cf3-a643-7a79ac353ace", "original_filename": "103732017-_d.pdf", "stored_filename": "4c401c26-bc51-4b15-8e89-b856e361cc71.pdf", "file_path": "app/static/uploads/history/4c401c26-bc51-4b15-8e89-b856e361cc71.pdf", "mime_type": "application/pdf", "file_size": 500320, "upload_date": "2025-07-22 14:19:49"}]}, {"id": "29567558-cf70-4b81-8066-dff66c9f9d6a", "equipment_id": "103732017", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The ENT workstation was relocated from the 5th floor to the 4th floor. \r\nThe unit was installed with all accessories and pipe connections. \r\nFunctionality was tested and confirmed to be working properly.", "created_at": "2025-07-22 14:24:45", "updated_at": "2025-07-22 14:24:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "99260891-c34d-4479-8b07-817cf7ccb25e", "note_id": "29567558-cf70-4b81-8066-dff66c9f9d6a", "original_filename": "103732017-_e.pdf", "stored_filename": "b2d8c208-0fdc-4c4e-8b7a-bfa782522dbc.pdf", "file_path": "app/static/uploads/history/b2d8c208-0fdc-4c4e-8b7a-bfa782522dbc.pdf", "mime_type": "application/pdf", "file_size": 513728, "upload_date": "2025-07-22 14:24:45"}]}, {"id": "b3e2b3b3-79bc-4651-ac5e-6ba613b1f5ed", "equipment_id": "209", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the neutral plate connection inside is broken. from the connector.\r\nsoldered the cable with the connector and fixed it.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 14:25:18", "updated_at": "2025-07-22 14:25:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b2dfb9cf-d8b4-4887-b59d-687e09cd273c", "note_id": "b3e2b3b3-79bc-4651-ac5e-6ba613b1f5ed", "original_filename": "209.pdf", "stored_filename": "ae4be6bf-c402-403f-a938-bc3b969cc295.pdf", "file_path": "app/static/uploads/history/ae4be6bf-c402-403f-a938-bc3b969cc295.pdf", "mime_type": "application/pdf", "file_size": 532176, "upload_date": "2025-07-22 14:25:18"}]}, {"id": "a078c5a8-6fe6-484e-b4fa-aea9d9d3539d", "equipment_id": "90116161101", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The audiometric booth was relocated from Alorf Hospital to Capital Hospital. \r\nThe unit was dismantled and safely delivered to Capital Hospital for storage.", "created_at": "2025-07-22 14:28:18", "updated_at": "2025-07-22 14:28:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "daab66c2-22f7-4e14-9f0a-a3b253da7901", "note_id": "a078c5a8-6fe6-484e-b4fa-aea9d9d3539d", "original_filename": "90116161101.pdf", "stored_filename": "154c810e-9558-4342-a611-5741bb0c85a9.pdf", "file_path": "app/static/uploads/history/154c810e-9558-4342-a611-5741bb0c85a9.pdf", "mime_type": "application/pdf", "file_size": 490656, "upload_date": "2025-07-22 14:28:18"}]}, {"id": "1700505f-48e9-49a1-9405-a28ac97444b9", "equipment_id": "NV111020091", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the oil is leaking.\r\ncleaned the base and found that the one tube cap is loose. \r\nTighten the cap and fix it properly.\r\nchecked and confirmed that the unit is working properly.\r\nRefill the unit with hydraulic oil next time.", "created_at": "2025-07-22 14:28:46", "updated_at": "2025-07-22 14:28:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a7e3a8ef-7a01-4b5a-b25c-55126986440a", "note_id": "1700505f-48e9-49a1-9405-a28ac97444b9", "original_filename": "NV111020091_C.pdf", "stored_filename": "ae12b189-dcb3-406c-86f7-2991073a4473.pdf", "file_path": "app/static/uploads/history/ae12b189-dcb3-406c-86f7-2991073a4473.pdf", "mime_type": "application/pdf", "file_size": 542512, "upload_date": "2025-07-22 14:28:46"}]}, {"id": "0accb734-86d9-4f23-95f6-579a1362861d", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the Enthermo mainboard and adjusted the pressure switch. \r\nThe unit was tested and confirmed to be functioning correctly.", "created_at": "2025-07-22 14:32:32", "updated_at": "2025-07-22 14:32:32", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3ffa6d9a-3f9d-41bd-8ed1-315765ff0ef5", "note_id": "0accb734-86d9-4f23-95f6-579a1362861d", "original_filename": "111632045_-l.pdf", "stored_filename": "5963f257-8476-45ad-b71e-044841006628.pdf", "file_path": "app/static/uploads/history/5963f257-8476-45ad-b71e-044841006628.pdf", "mime_type": "application/pdf", "file_size": 519616, "upload_date": "2025-07-22 14:32:32"}]}, {"id": "ac9bfa8f-2b4a-42e0-80b0-34f824310512", "equipment_id": "IMIDD17203836", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found that the connections were not properly secured. \r\nReconnected all modalities and tested the audiometer. \r\nThe unit is now functioning properly.", "created_at": "2025-07-22 14:36:42", "updated_at": "2025-07-22 14:36:42", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "78a8726a-6ed0-4556-a50b-08847d9d8d57", "note_id": "ac9bfa8f-2b4a-42e0-80b0-34f824310512", "original_filename": "IMIDD17203836-_b.pdf", "stored_filename": "1c970f4e-cbb1-4a38-bd66-be42<PERSON><PERSON><PERSON>e5.pdf", "file_path": "app/static/uploads/history/1c970f4e-cbb1-4a38-bd66-be42deebcee5.pdf", "mime_type": "application/pdf", "file_size": 525552, "upload_date": "2025-07-22 14:36:42"}]}, {"id": "80f68bad-6657-4f06-afb8-5cde0056642f", "equipment_id": "111632045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Upon inspection, it was found that the water irrigation connection was detached from the handpiece. \r\nThe connector was reattached, and the system was checked for leaks. \r\nNo leakage was observed, and the unit is functioning properly.", "created_at": "2025-07-22 14:40:06", "updated_at": "2025-07-22 14:40:06", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bf9e531d-f8ac-415d-a434-1bfe5d33314c", "note_id": "80f68bad-6657-4f06-afb8-5cde0056642f", "original_filename": "111632045-_m.pdf", "stored_filename": "1a61a33b-9a2a-4875-9863-8c1e2c3088cb.pdf", "file_path": "app/static/uploads/history/1a61a33b-9a2a-4875-9863-8c1e2c3088cb.pdf", "mime_type": "application/pdf", "file_size": 506288, "upload_date": "2025-07-22 14:40:06"}]}, {"id": "890eff52-d668-4922-8b67-0a15d585a396", "equipment_id": "INIDD17203029", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "For recalibration.", "created_at": "2025-07-22 14:42:19", "updated_at": "2025-07-22 14:42:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d28d7b3b-0b01-4358-81f8-dbe8d45fec85", "note_id": "890eff52-d668-4922-8b67-0a15d585a396", "original_filename": "INIDD17203029.pdf", "stored_filename": "fc48ad40-4cc1-4615-8b54-65ad2e3ff56c.pdf", "file_path": "app/static/uploads/history/fc48ad40-4cc1-4615-8b54-65ad2e3ff56c.pdf", "mime_type": "application/pdf", "file_size": 417472, "upload_date": "2025-07-22 14:42:19"}]}, {"id": "d0d14bb3-5810-47be-b5fc-00deae0a1e69", "equipment_id": "IMIDD17203836", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "For recalibration.", "created_at": "2025-07-22 14:44:01", "updated_at": "2025-07-22 14:44:01", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8d581801-3858-446a-b3d8-dc5a90fb1215", "note_id": "d0d14bb3-5810-47be-b5fc-00deae0a1e69", "original_filename": "IMIDD17203836-_c.pdf", "stored_filename": "c876b2f1-0adc-4733-aad3-b198e1ddab6f.pdf", "file_path": "app/static/uploads/history/c876b2f1-0adc-4733-aad3-b198e1ddab6f.pdf", "mime_type": "application/pdf", "file_size": 421552, "upload_date": "2025-07-22 14:44:01"}]}, {"id": "8b35f0a9-5f14-4ce4-8387-2d3e820f0dd4", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The Chemistry \"A\" pump was found to be defective and required replacement. \r\nAdditionally, several solenoid valves showed signs of malfunction. \r\nThe Chemistry \"A\" pump was replaced with a standby unit. \r\nA complete cycle was run and successfully completed, confirming the unit is now functioning properly.", "created_at": "2025-07-22 14:47:34", "updated_at": "2025-07-22 14:47:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fc0c6dc4-51c8-411d-934b-e7ae2ba4d5c2", "note_id": "8b35f0a9-5f14-4ce4-8387-2d3e820f0dd4", "original_filename": "25186.pdf", "stored_filename": "2d7e36f2-e6b1-4473-9c03-f916fa3bffae.pdf", "file_path": "app/static/uploads/history/2d7e36f2-e6b1-4473-9c03-f916fa3bffae.pdf", "mime_type": "application/pdf", "file_size": 569472, "upload_date": "2025-07-22 14:47:34"}]}, {"id": "874387c2-1499-4f6d-8b09-564b89da8922", "equipment_id": "118041", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "returned the unit with the adapter.\r\nfound that the drive is faulty, the CMOS battery is defective, and there is a software issue.\r\nReplace the drive and CMOS battery with new ones.\r\nReloaded software.\r\nchecked and found it is working in good condition.", "created_at": "2025-07-22 14:51:26", "updated_at": "2025-07-22 14:51:26", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c75232c3-f098-4d75-80e3-d2d0381a9627", "note_id": "874387c2-1499-4f6d-8b09-564b89da8922", "original_filename": "118041.pdf", "stored_filename": "344a2461-cb82-45e6-bdfd-2564064dfc2e.pdf", "file_path": "app/static/uploads/history/344a2461-cb82-45e6-bdfd-2564064dfc2e.pdf", "mime_type": "application/pdf", "file_size": 562944, "upload_date": "2025-07-22 14:51:26"}]}, {"id": "3bc97cb7-e6c3-4dc3-ba1d-92b5d979da7d", "equipment_id": "C800152102", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "V2 and V3 leads were not detecting.\r\nUpon inspection, the lead ends were found to be dirty. \r\nThe leads were cleaned, and the lead connector was replaced with a spare from the store. \r\nAll connectors were tested, and the system is now functioning properly.", "created_at": "2025-07-22 14:51:31", "updated_at": "2025-07-22 14:51:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "eb513625-a45e-4037-97f5-bbaf910b49c6", "note_id": "3bc97cb7-e6c3-4dc3-ba1d-92b5d979da7d", "original_filename": "C800152102.pdf", "stored_filename": "d490328e-7b00-4ed9-80fc-862422242b88.pdf", "file_path": "app/static/uploads/history/d490328e-7b00-4ed9-80fc-862422242b88.pdf", "mime_type": "application/pdf", "file_size": 525920, "upload_date": "2025-07-22 14:51:31"}]}, {"id": "419afe3f-da79-473b-8f62-d17cf2630a97", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the water filter sets and air filter with new ones. \r\nCompleted Cycle 6 successfully. \r\nThe unit is operating in good condition.", "created_at": "2025-07-22 14:54:17", "updated_at": "2025-07-22 14:54:17", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ba435f7c-3372-4e19-8d8f-3c4213fc181d", "note_id": "419afe3f-da79-473b-8f62-d17cf2630a97", "original_filename": "25186-_b.pdf", "stored_filename": "4b89d125-0821-4415-a329-65b686264d2e.pdf", "file_path": "app/static/uploads/history/4b89d125-0821-4415-a329-65b686264d2e.pdf", "mime_type": "application/pdf", "file_size": 528688, "upload_date": "2025-07-22 14:54:17"}]}, {"id": "3d04f41d-cb0c-4b0c-93f5-3a9355a80930", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found damaged tubing for Sol ‘A’ and Sol ‘P’, as well as a defective Sol ‘A’ pump. Replaced the faulty components and resolved the leakage in the closing chamber by replacing the NRV. Completed the cycle successfully and confirmed the unit is working properly.", "created_at": "2025-07-22 15:01:52", "updated_at": "2025-07-22 15:01:52", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b661c3a8-1be7-4e31-aab4-7c41016bfb96", "note_id": "3d04f41d-cb0c-4b0c-93f5-3a9355a80930", "original_filename": "25186-_c.pdf", "stored_filename": "4a4ef049-528e-4d03-82fc-20d865cfe8ee.pdf", "file_path": "app/static/uploads/history/4a4ef049-528e-4d03-82fc-20d865cfe8ee.pdf", "mime_type": "application/pdf", "file_size": 639232, "upload_date": "2025-07-22 15:01:52"}]}, {"id": "64d02b42-892f-44b5-a1f8-fd26f95e9298", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The pump for Sol ‘A’ was found to be defective and requires replacement.", "created_at": "2025-07-22 15:03:20", "updated_at": "2025-07-22 15:03:20", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "387c0842-52a0-43e7-8a0d-6c80d2b4797f", "note_id": "64d02b42-892f-44b5-a1f8-fd26f95e9298", "original_filename": "25186-_d.pdf", "stored_filename": "ef126986-c9e1-44ac-9df1-8aec5677e06d.pdf", "file_path": "app/static/uploads/history/ef126986-c9e1-44ac-9df1-8aec5677e06d.pdf", "mime_type": "application/pdf", "file_size": 521632, "upload_date": "2025-07-22 15:03:20"}]}, {"id": "41e375ab-3384-4551-9d48-613e3c42ae60", "equipment_id": "1010-2-203", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Routine maintenance and checking for the laser are done.\r\nchecking the gas system.\r\nchecking ET & fluence test.\r\nChecking energy and functions okay.\r\nAttend operations with the doctors.\r\nsystem is okay.", "created_at": "2025-07-22 15:08:22", "updated_at": "2025-07-22 15:08:22", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2776ed28-f156-42c5-a96e-da340e3612c1", "note_id": "41e375ab-3384-4551-9d48-613e3c42ae60", "original_filename": "1010-2-203_C.pdf", "stored_filename": "cd7a3295-9386-4ad9-9f2d-65e223d81340.pdf", "file_path": "app/static/uploads/history/cd7a3295-9386-4ad9-9f2d-65e223d81340.pdf", "mime_type": "application/pdf", "file_size": 454688, "upload_date": "2025-07-22 15:08:22"}]}, {"id": "69e1ffa3-6f59-4648-9f01-0419b8f9e06c", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "During inspection, error 12-30 was detected. \r\nReplaced three NRVs in the solution injection line and renewed all related connections. \r\nUpon inspection, the solution injection pump was found to be faulty and requires replacement. \r\nThe unit is currently not operational.", "created_at": "2025-07-22 15:10:54", "updated_at": "2025-07-22 15:10:54", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8eadf00c-bd9f-43a3-920e-7cab7c12ec97", "note_id": "69e1ffa3-6f59-4648-9f01-0419b8f9e06c", "original_filename": "25186-_e.pdf", "stored_filename": "a25bb796-6e45-443e-b582-e12a0844a687.pdf", "file_path": "app/static/uploads/history/a25bb796-6e45-443e-b582-e12a0844a687.pdf", "mime_type": "application/pdf", "file_size": 517984, "upload_date": "2025-07-22 15:10:54"}]}, {"id": "27c9a373-4819-4e80-84a2-32f5097a5eb0", "equipment_id": "EO3CNA00008", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "fixed the side plate.\r\nconnected the other side to the table.\r\nChecked all the connections are clear and not damaged.\r\nunit is fixed.", "created_at": "2025-07-22 15:13:48", "updated_at": "2025-07-22 15:13:48", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "454f8b93-6903-42d8-afc0-3be1030b20de", "note_id": "27c9a373-4819-4e80-84a2-32f5097a5eb0", "original_filename": "EO3CNA00008.pdf", "stored_filename": "9ca0c339-def5-4dd0-9822-192f5426eba0.pdf", "file_path": "app/static/uploads/history/9ca0c339-def5-4dd0-9822-192f5426eba0.pdf", "mime_type": "application/pdf", "file_size": 519872, "upload_date": "2025-07-22 15:13:48"}]}, {"id": "956da93b-ff55-4d5c-ba36-c388847907a6", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The suction injection pump was found to be faulty and was replaced with a new one. \r\nBottle connections were also replaced. \r\nAdditionally, the inlet solution NRV and the injection-side NRV were replaced. \r\nCycle 6 was performed successfully, and the unit has been confirmed to be working properly.", "created_at": "2025-07-22 15:14:11", "updated_at": "2025-07-22 15:14:11", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "348e1bb7-71d3-484a-92be-2c80fb8af072", "note_id": "956da93b-ff55-4d5c-ba36-c388847907a6", "original_filename": "25186-_f.pdf", "stored_filename": "4f3d416d-2d78-4736-b456-f5dd5de874e3.pdf", "file_path": "app/static/uploads/history/4f3d416d-2d78-4736-b456-f5dd5de874e3.pdf", "mime_type": "application/pdf", "file_size": 541728, "upload_date": "2025-07-22 15:14:11"}]}, {"id": "40111704-fbfd-40ff-8fe7-e6e7f657f777", "equipment_id": "116", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Change the water with new.\r\nremoved air bubbles from the balloons.\r\nperformed maintenance for the unit and tests done.\r\nchecked and found that the unit is working properly.", "created_at": "2025-07-22 15:16:00", "updated_at": "2025-07-22 15:16:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "664c5527-22bb-489b-8ca8-e47ffe15d7c6", "note_id": "40111704-fbfd-40ff-8fe7-e6e7f657f777", "original_filename": "116.pdf", "stored_filename": "9f754ccf-3326-47ab-bf53-e865a27f3126.pdf", "file_path": "app/static/uploads/history/9f754ccf-3326-47ab-bf53-e865a27f3126.pdf", "mime_type": "application/pdf", "file_size": 549024, "upload_date": "2025-07-22 15:16:00"}]}, {"id": "1f8da66d-0b36-429b-8e25-84f3449a19a2", "equipment_id": "25186", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Error 111 was detected, caused by trapped air in the closing chamber. \r\nThe closing chamber connection was removed to release the air, then reconnected. \r\nA test cycle was performed successfully, and the unit was confirmed to be working properly.", "created_at": "2025-07-22 15:17:36", "updated_at": "2025-07-22 15:17:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2e29d433-d40d-4372-b237-76cd33cd6b41", "note_id": "1f8da66d-0b36-429b-8e25-84f3449a19a2", "original_filename": "25186-_g.pdf", "stored_filename": "0a780dd1-68a4-4eae-a784-fd2c1d21e1c1.pdf", "file_path": "app/static/uploads/history/0a780dd1-68a4-4eae-a784-fd2c1d21e1c1.pdf", "mime_type": "application/pdf", "file_size": 527696, "upload_date": "2025-07-22 15:17:36"}]}, {"id": "6df1d44b-519a-4d17-b085-2edfaba48275", "equipment_id": "NV111020091", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the screws are loose.\r\ntightened the screws and checked the unit.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 15:18:09", "updated_at": "2025-07-22 15:18:09", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "85eb7fdf-80bf-4c0c-a0eb-146032f1acb9", "note_id": "6df1d44b-519a-4d17-b085-2edfaba48275", "original_filename": "NV111020091_D.pdf", "stored_filename": "19faede9-15ca-4b30-aeeb-1374500fab1b.pdf", "file_path": "app/static/uploads/history/19faede9-15ca-4b30-aeeb-1374500fab1b.pdf", "mime_type": "application/pdf", "file_size": 513328, "upload_date": "2025-07-22 15:18:09"}]}, {"id": "e290a010-8352-499b-bc13-61e0793<PERSON>da", "equipment_id": "208", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the connection for the surgical handpiece connector is broken.\r\nrepaired the connector and fixed it properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 15:20:17", "updated_at": "2025-07-22 15:20:17", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5271cf0d-7e5d-4af7-8098-fead508eb50c", "note_id": "e290a010-8352-499b-bc13-61e0793<PERSON>da", "original_filename": "208_B.pdf", "stored_filename": "51576bff-5e0a-4b34-9951-47bc7b52a9b2.pdf", "file_path": "app/static/uploads/history/51576bff-5e0a-4b34-9951-47bc7b52a9b2.pdf", "mime_type": "application/pdf", "file_size": 542768, "upload_date": "2025-07-22 15:20:17"}]}, {"id": "6501484b-c4bf-4330-bb73-3325ca1ff3cb", "equipment_id": "111057S60M", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Part Number; FR 200 983", "created_at": "2025-07-22 15:20:52", "updated_at": "2025-07-22 15:20:52", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "59571188-279d-4047-9248-8c6b2f75ee9c", "note_id": "6501484b-c4bf-4330-bb73-3325ca1ff3cb", "original_filename": "111057S60M.pdf", "stored_filename": "c55f3bac-0209-4078-900f-634e93be4bba.pdf", "file_path": "app/static/uploads/history/c55f3bac-0209-4078-900f-634e93be4bba.pdf", "mime_type": "application/pdf", "file_size": 625536, "upload_date": "2025-07-22 15:20:52"}]}, {"id": "b4fc1eef-5d44-4204-b1bb-40a977ea9717", "equipment_id": "2202833", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Item delivered: Power Supply - Smart Flow\r\nPart Number: 0004423", "created_at": "2025-07-22 15:22:43", "updated_at": "2025-07-22 15:22:43", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c273f760-7652-42cd-9c41-12d408d418e0", "note_id": "b4fc1eef-5d44-4204-b1bb-40a977ea9717", "original_filename": "2202833-_b.pdf", "stored_filename": "e3658f89-1509-4906-9a7f-c14fb6d67255.pdf", "file_path": "app/static/uploads/history/e3658f89-1509-4906-9a7f-c14fb6d67255.pdf", "mime_type": "application/pdf", "file_size": 1316703, "upload_date": "2025-07-22 15:22:43"}]}, {"id": "e1402f2c-be0a-4587-9713-03f04dd59330", "equipment_id": "NV111020091", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the remote and found that the cable is broken.\r\nTake the remote for inspection in the workshop.\r\nwaiting for the update to see if they can fix it or not.", "created_at": "2025-07-22 15:22:55", "updated_at": "2025-07-22 15:22:55", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "15e64701-8095-4161-83a0-47c757de8cb7", "note_id": "e1402f2c-be0a-4587-9713-03f04dd59330", "original_filename": "log_no._2161.pdf", "stored_filename": "ca1888d9-9da2-4e1b-9aa9-f0389e3d1a05.pdf", "file_path": "app/static/uploads/history/ca1888d9-9da2-4e1b-9aa9-f0389e3d1a05.pdf", "mime_type": "application/pdf", "file_size": 514016, "upload_date": "2025-07-22 15:22:55"}]}, {"id": "93acef81-c4b9-4148-a21f-1b1686e5adef", "equipment_id": "118041", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "taking the unit to the company for repair with the power adapter.", "created_at": "2025-07-22 15:24:34", "updated_at": "2025-07-22 15:24:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "18ea8c96-cdb1-46f6-bf91-61536d950afd", "note_id": "93acef81-c4b9-4148-a21f-1b1686e5adef", "original_filename": "118041_B.pdf", "stored_filename": "9e74cad0-95ac-4790-b82d-4d51f570972b.pdf", "file_path": "app/static/uploads/history/9e74cad0-95ac-4790-b82d-4d51f570972b.pdf", "mime_type": "application/pdf", "file_size": 507056, "upload_date": "2025-07-22 15:24:34"}]}, {"id": "e2a7520f-fbdf-4468-92e6-e7bebe67e3ca", "equipment_id": "M1521040056", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that there is no battery installed.\r\nNeed to install a lithium battery.\r\nNeed to order a new battery.", "created_at": "2025-07-22 15:27:00", "updated_at": "2025-07-22 15:27:12", "last_modified_by": "admin", "last_modified_by_name": "admin", "is_edited": true, "attachments": [{"id": "5c1663f4-b556-4e68-89df-2b1d058944d0", "note_id": "e2a7520f-fbdf-4468-92e6-e7bebe67e3ca", "original_filename": "M1521040056.pdf", "stored_filename": "b063fa5d-f804-4977-9d31-2a3109ea1000.pdf", "file_path": "app/static/uploads/history/b063fa5d-f804-4977-9d31-2a3109ea1000.pdf", "mime_type": "application/pdf", "file_size": 553408, "upload_date": "2025-07-22 15:27:00"}]}, {"id": "59e4f862-1901-4e3d-84cf-a2cabe2e59f0", "equipment_id": "19101628", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the interface module for the G-Runner with a new one.\r\nAll functions were tested and confirmed to be working correctly. \r\nThe unit is in good condition.", "created_at": "2025-07-22 15:28:59", "updated_at": "2025-07-22 15:28:59", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3ae00807-4923-4ff6-a725-b1ec203ed983", "note_id": "59e4f862-1901-4e3d-84cf-a2cabe2e59f0", "original_filename": "19101628-_c.pdf", "stored_filename": "0bdbe6f8-2e1a-46a7-9962-c68ac38e1745.pdf", "file_path": "app/static/uploads/history/0bdbe6f8-2e1a-46a7-9962-c68ac38e1745.pdf", "mime_type": "application/pdf", "file_size": 520272, "upload_date": "2025-07-22 15:28:59"}]}, {"id": "330b18e7-bcc3-4e16-a4ea-35d221e7a382", "equipment_id": "N-A-182", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the cooling fans for two units of the voltage regulator. \r\nThe units were tested and confirmed to be operating in good condition.", "created_at": "2025-07-22 15:35:19", "updated_at": "2025-07-22 15:35:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "92a5614e-3951-447b-aecb-395105765334", "note_id": "330b18e7-bcc3-4e16-a4ea-35d221e7a382", "original_filename": "LOG__1805.pdf", "stored_filename": "512c5387-202d-4b79-ad1f-740c12ee7af9.pdf", "file_path": "app/static/uploads/history/512c5387-202d-4b79-ad1f-740c12ee7af9.pdf", "mime_type": "application/pdf", "file_size": 485376, "upload_date": "2025-07-22 15:35:19"}]}, {"id": "fd9aebed-bcae-4f45-a6f2-58bcc18894c7", "equipment_id": "LAE21006019", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Faulty interface module (Part No. 21000058) was identified and replaced with a new module (Part No. 21000636).\r\nThe G-Runner was tested and confirmed to be working properly.", "created_at": "2025-07-22 15:39:16", "updated_at": "2025-07-22 15:39:16", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "c60ce48c-9446-4cf0-881e-287b931c9c1b", "note_id": "fd9aebed-bcae-4f45-a6f2-58bcc18894c7", "original_filename": "LOG__1697.pdf", "stored_filename": "36976930-67e9-4040-9387-214566365ed4.pdf", "file_path": "app/static/uploads/history/36976930-67e9-4040-9387-214566365ed4.pdf", "mime_type": "application/pdf", "file_size": 483920, "upload_date": "2025-07-22 15:39:16"}]}, {"id": "5d9f3320-7c15-433e-a3c5-af3da601ba32", "equipment_id": "19101628", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Checked the laser output using G-Runner 22 and found that the laser was not active. \r\nTested the unit directly without the interface and scanner, and it functioned properly.\r\n This indicates a likely issue with the G-Runner interface.\r\n The G-Runner has been taken to the workshop for further inspection.", "created_at": "2025-07-22 15:43:43", "updated_at": "2025-07-22 15:43:43", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8dc67401-a1af-4066-a4e4-6fdadf9b2e37", "note_id": "5d9f3320-7c15-433e-a3c5-af3da601ba32", "original_filename": "19101628-_d.pdf", "stored_filename": "eb03ebc4-6758-48d1-bdf8-79e433a6f43d.pdf", "file_path": "app/static/uploads/history/eb03ebc4-6758-48d1-bdf8-79e433a6f43d.pdf", "mime_type": "application/pdf", "file_size": 549408, "upload_date": "2025-07-22 15:43:43"}]}, {"id": "a60a5aef-3dc3-483c-a767-3c989d82e248", "equipment_id": "K6212070045", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The ceiling examination lamp was reinstalled in the other room.\r\nChecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 15:45:45", "updated_at": "2025-07-22 15:45:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8a7a8802-a77d-46b9-853c-104f3dfe94d4", "note_id": "a60a5aef-3dc3-483c-a767-3c989d82e248", "original_filename": "K6212070045.pdf", "stored_filename": "d4daa15f-13b2-4569-ab40-967ce5db6ba4.pdf", "file_path": "app/static/uploads/history/d4daa15f-13b2-4569-ab40-967ce5db6ba4.pdf", "mime_type": "application/pdf", "file_size": 461328, "upload_date": "2025-07-22 15:45:45"}]}, {"id": "e89ce44a-e2e3-41a0-8428-994a65209f4b", "equipment_id": "19101628", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Returned the G-Runner along with its accessories. \r\nThe issue was identified as a faulty interface controller. \r\nThe controller was replaced under warranty.\r\n The unit was tested and confirmed to be working properly.", "created_at": "2025-07-22 15:46:57", "updated_at": "2025-07-22 15:46:57", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7186a694-f6a0-4063-bd15-47fc86289a7a", "note_id": "e89ce44a-e2e3-41a0-8428-994a65209f4b", "original_filename": "19101628-e.pdf", "stored_filename": "4a422af1-77f9-4040-92d5-bfcd5d2a3ac3.pdf", "file_path": "app/static/uploads/history/4a422af1-77f9-4040-92d5-bfcd5d2a3ac3.pdf", "mime_type": "application/pdf", "file_size": 540144, "upload_date": "2025-07-22 15:46:57"}]}, {"id": "26d6ecbc-f52b-4533-9216-65e6e6fbc2c2", "equipment_id": "208", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and fixed the neutral connection port.\r\nproperly connected the port with a nut.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 15:49:44", "updated_at": "2025-07-22 15:49:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a2f39144-1248-40e1-9a0c-4c4e34221dee", "note_id": "26d6ecbc-f52b-4533-9216-65e6e6fbc2c2", "original_filename": "208_C.pdf", "stored_filename": "d278ffbd-5032-4338-9167-b366b974908b.pdf", "file_path": "app/static/uploads/history/d278ffbd-5032-4338-9167-b366b974908b.pdf", "mime_type": "application/pdf", "file_size": 472544, "upload_date": "2025-07-22 15:49:44"}]}, {"id": "690917c4-33c1-4046-929d-933be37359e4", "equipment_id": "16206557", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected the ECG unit and found the system time was incorrect. \r\nThe time was reset, and the unit was tested.\r\nFunctionality was confirmed to be proper.", "created_at": "2025-07-22 15:50:27", "updated_at": "2025-07-22 15:50:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a116b4d8-dfac-43d9-a802-659a92aa7fde", "note_id": "690917c4-33c1-4046-929d-933be37359e4", "original_filename": "16206557.pdf", "stored_filename": "975c54ca-3372-40a4-9f94-b96113315eb9.pdf", "file_path": "app/static/uploads/history/975c54ca-3372-40a4-9f94-b96113315eb9.pdf", "mime_type": "application/pdf", "file_size": 441472, "upload_date": "2025-07-22 15:50:27"}]}, {"id": "c54aa94d-1bc5-46d9-acfc-f1b8e7f4ba4e", "equipment_id": "550", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected the unit and found the cuff (small) is leaking. \r\nNeed to change the cuff.", "created_at": "2025-07-22 15:51:13", "updated_at": "2025-07-22 15:51:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0847dda5-532e-447c-adcd-24906d1e536c", "note_id": "c54aa94d-1bc5-46d9-acfc-f1b8e7f4ba4e", "original_filename": "550.pdf", "stored_filename": "a6990d3e-4f50-4d2d-9377-53a3366ab76e.pdf", "file_path": "app/static/uploads/history/a6990d3e-4f50-4d2d-9377-53a3366ab76e.pdf", "mime_type": "application/pdf", "file_size": 451568, "upload_date": "2025-07-22 15:51:13"}]}, {"id": "c207fef0-8fab-4460-86fe-f8dba68d04ef", "equipment_id": "05AF000643", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Repaired NIBP connector.\r\nunit is working properly.", "created_at": "2025-07-22 15:53:03", "updated_at": "2025-07-22 15:53:03", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "40b5c77a-9dfb-4459-b1a1-fa92e5bbddc0", "note_id": "c207fef0-8fab-4460-86fe-f8dba68d04ef", "original_filename": "05AF000643.pdf", "stored_filename": "c0f839b0-332b-466e-9fdb-32156f020919.pdf", "file_path": "app/static/uploads/history/c0f839b0-332b-466e-9fdb-32156f020919.pdf", "mime_type": "application/pdf", "file_size": 441072, "upload_date": "2025-07-22 15:53:03"}]}, {"id": "f0d83b82-ca4d-4109-b361-ad5aeb8ea1a1", "equipment_id": "1010-2-203", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "routine maintenance check\r\ncheck the gas system\r\ngas refill and activate laser\r\ncheck energy\r\nCheck different functions.\r\nsystem is working okay.", "created_at": "2025-07-22 15:58:18", "updated_at": "2025-07-22 15:58:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fbe6b20a-d192-46bf-bf27-fd74a8650988", "note_id": "f0d83b82-ca4d-4109-b361-ad5aeb8ea1a1", "original_filename": "1010-2-203_D.pdf", "stored_filename": "262974a4-ec00-44ea-ae08-af467f5efbc8.pdf", "file_path": "app/static/uploads/history/262974a4-ec00-44ea-ae08-af467f5efbc8.pdf", "mime_type": "application/pdf", "file_size": 453136, "upload_date": "2025-07-22 15:58:18"}]}, {"id": "521dd9de-4ff6-40cb-a266-4e667f9e3f9b", "equipment_id": "29578", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced the power board with a new one.\r\nchecked all functions and found it is working properly.", "created_at": "2025-07-22 16:05:13", "updated_at": "2025-07-22 16:05:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "36cc62df-9710-4456-8f51-14ca90e04244", "note_id": "521dd9de-4ff6-40cb-a266-4e667f9e3f9b", "original_filename": "029578.pdf", "stored_filename": "05509273-3c72-4d99-8750-9541a7ae9740.pdf", "file_path": "app/static/uploads/history/05509273-3c72-4d99-8750-9541a7ae9740.pdf", "mime_type": "application/pdf", "file_size": 457152, "upload_date": "2025-07-22 16:05:13"}]}, {"id": "d69d35e6-1c74-4f65-b45e-c8fcb7cdcbdb", "equipment_id": "1010-2-203", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "PM & check was done.\r\ngas exchange and activating the laser head.\r\nenergy check\r\nchecked the gas system and eye tracker and fluence.\r\nchecked different functions of the systems\r\nsystem is working okay.", "created_at": "2025-07-22 16:12:12", "updated_at": "2025-07-22 16:12:12", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a4857cfc-9440-4e97-9525-475f14ec5a2a", "note_id": "d69d35e6-1c74-4f65-b45e-c8fcb7cdcbdb", "original_filename": "1010-2-203_E.pdf", "stored_filename": "71c19b78-3d5f-41b4-a622-e1977efbb275.pdf", "file_path": "app/static/uploads/history/71c19b78-3d5f-41b4-a622-e1977efbb275.pdf", "mime_type": "application/pdf", "file_size": 494944, "upload_date": "2025-07-22 16:12:12"}]}, {"id": "b0710878-fad9-4a3a-98c1-c9ddf2d0786e", "equipment_id": "D42", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "tested the machine, and it's working in good condition.", "created_at": "2025-07-22 16:14:03", "updated_at": "2025-07-22 16:14:03", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3be6d778-eeb9-40df-a8b1-cfab936cc590", "note_id": "b0710878-fad9-4a3a-98c1-c9ddf2d0786e", "original_filename": "D42.pdf", "stored_filename": "a9c5ba53-b18d-466d-8fa4-45125c3f605e.pdf", "file_path": "app/static/uploads/history/a9c5ba53-b18d-466d-8fa4-45125c3f605e.pdf", "mime_type": "application/pdf", "file_size": 382992, "upload_date": "2025-07-22 16:14:03"}]}, {"id": "25630181-4bad-440f-9418-8608c6e66e04", "equipment_id": "1596853", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "found the vacuum bottle inside the unit filled with dust particles.\r\ncleaned the bottle.\r\nchecked and found the unit is working properly.", "created_at": "2025-07-22 16:16:10", "updated_at": "2025-07-22 16:16:10", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "651d0fdf-5994-427d-a764-0a49708cb0c2", "note_id": "25630181-4bad-440f-9418-8608c6e66e04", "original_filename": "1596853.pdf", "stored_filename": "a6ded52a-b6ba-4c63-874b-f26769e50004.pdf", "file_path": "app/static/uploads/history/a6ded52a-b6ba-4c63-874b-f26769e50004.pdf", "mime_type": "application/pdf", "file_size": 527728, "upload_date": "2025-07-22 16:16:10"}]}, {"id": "8995d1a7-c462-4110-a8d9-2ebb237e4fe1", "equipment_id": "05-095202", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and checked all its functions, found that the unit is working properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 16:19:55", "updated_at": "2025-07-22 16:19:55", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ae8303ff-50fa-4b09-a3e7-b7cb69ccaec1", "note_id": "8995d1a7-c462-4110-a8d9-2ebb237e4fe1", "original_filename": "05-095202.pdf", "stored_filename": "78c784d5-cf71-49a4-91f6-88f39f549f86.pdf", "file_path": "app/static/uploads/history/78c784d5-cf71-49a4-91f6-88f39f549f86.pdf", "mime_type": "application/pdf", "file_size": 536944, "upload_date": "2025-07-22 16:19:55"}]}, {"id": "f376f178-393c-40c6-adc6-661d68605a93", "equipment_id": "E0130018", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Found the foot switch to be non-functional. \r\nIt has been taken to the workshop for repair.", "created_at": "2025-07-22 16:21:56", "updated_at": "2025-07-22 16:21:56", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "6b48a238-655c-4c05-a34b-b2fac22edb5c", "note_id": "f376f178-393c-40c6-adc6-661d68605a93", "original_filename": "LOG__1679-_b.pdf", "stored_filename": "739a9a44-51ea-456c-9ce7-a6cd63d72b98.pdf", "file_path": "app/static/uploads/history/739a9a44-51ea-456c-9ce7-a6cd63d72b98.pdf", "mime_type": "application/pdf", "file_size": 764126, "upload_date": "2025-07-22 16:21:56"}]}, {"id": "64b55e18-7aab-4f39-98bc-c4ee274f3a87", "equipment_id": "NV111020091", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the hydraulic oil level is low. \r\nrefill the hydraulic oil\r\nThe unit now is working properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 16:23:10", "updated_at": "2025-07-22 16:23:10", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "adc29f7c-46d2-4375-bd22-32f4d1bd5537", "note_id": "64b55e18-7aab-4f39-98bc-c4ee274f3a87", "original_filename": "NV111020091_E.pdf", "stored_filename": "45de7634-003d-4f11-ae7f-7892462035af.pdf", "file_path": "app/static/uploads/history/45de7634-003d-4f11-ae7f-7892462035af.pdf", "mime_type": "application/pdf", "file_size": 521616, "upload_date": "2025-07-22 16:23:10"}]}, {"id": "0b4a337f-7622-44a2-a332-adda6a6cc7a9", "equipment_id": "E0130018", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "The foot switch was found to be defective and has been repaired. \r\nAll connections were tested, and the bed is now functioning properly.", "created_at": "2025-07-22 16:23:29", "updated_at": "2025-07-22 16:23:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "abcd4c87-41a3-4a0a-921e-bddd20817396", "note_id": "0b4a337f-7622-44a2-a332-adda6a6cc7a9", "original_filename": "LOG__1679-_c.pdf", "stored_filename": "f8eba591-6be1-4d9b-8c5f-b871a685d530.pdf", "file_path": "app/static/uploads/history/f8eba591-6be1-4d9b-8c5f-b871a685d530.pdf", "mime_type": "application/pdf", "file_size": 769086, "upload_date": "2025-07-22 16:23:29"}]}, {"id": "1b0deff6-7c96-412b-afd6-e7ac2f618a6c", "equipment_id": "1508-01400", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the light source cable is removed from the main source.\r\nFixed the light source cable, and the unit is working properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 16:27:22", "updated_at": "2025-07-22 16:27:22", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "77709c19-2184-4e84-8035-d7c0efcb78ea", "note_id": "1b0deff6-7c96-412b-afd6-e7ac2f618a6c", "original_filename": "1508-01400.pdf", "stored_filename": "fbe7a7ff-c6b0-4bd8-b227-cb71bb3fdf3c.pdf", "file_path": "app/static/uploads/history/fbe7a7ff-c6b0-4bd8-b227-cb71bb3fdf3c.pdf", "mime_type": "application/pdf", "file_size": 535040, "upload_date": "2025-07-22 16:27:22"}]}, {"id": "7ad9180e-62c0-406c-a209-cb06c04caf43", "equipment_id": "14093-0311", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit, and it shows the alarm CO2 sensor defect.\r\nThe CO₂ sensor is faulty.\r\nneed to replace the CO₂ sensor.\r\nquotation will be given.", "created_at": "2025-07-22 16:30:00", "updated_at": "2025-07-22 16:30:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d4d1310b-6cc7-47f8-9437-98de6c00c0cf", "note_id": "7ad9180e-62c0-406c-a209-cb06c04caf43", "original_filename": "14093-0311.pdf", "stored_filename": "73f547df-b228-4f26-8ecc-f751caa9bd96.pdf", "file_path": "app/static/uploads/history/73f547df-b228-4f26-8ecc-f751caa9bd96.pdf", "mime_type": "application/pdf", "file_size": 458816, "upload_date": "2025-07-22 16:30:00"}]}, {"id": "b97c7663-625d-430b-a787-5133287f07ea", "equipment_id": "14876-0391", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced faulty CO₂ sensor.\r\ncalibrated CO₂ at 6% and O₂ at 21%.\r\n tested temperature and confirmed unit is working properly.", "created_at": "2025-07-22 16:32:37", "updated_at": "2025-07-22 16:32:37", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ae8b8ac8-b3ec-41e0-94c2-7b077048b8b3", "note_id": "b97c7663-625d-430b-a787-5133287f07ea", "original_filename": "14093-0391.pdf", "stored_filename": "30fae4af-194b-4810-97b5-ae316a0eb903.pdf", "file_path": "app/static/uploads/history/30fae4af-194b-4810-97b5-ae316a0eb903.pdf", "mime_type": "application/pdf", "file_size": 461360, "upload_date": "2025-07-22 16:32:37"}]}, {"id": "c49042fc-5164-495c-9751-00e85f670a96", "equipment_id": "14093-0846", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "shifted the position of two incubators as per the user request and confirmed they were working okay.", "created_at": "2025-07-22 16:35:29", "updated_at": "2025-07-22 16:35:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0a7da077-72f8-47ad-8202-5c0b48f37687", "note_id": "c49042fc-5164-495c-9751-00e85f670a96", "original_filename": "14093-0846.pdf", "stored_filename": "a1dad051-b6c0-4440-889f-f6c910debce5.pdf", "file_path": "app/static/uploads/history/a1dad051-b6c0-4440-889f-f6c910debce5.pdf", "mime_type": "application/pdf", "file_size": 491424, "upload_date": "2025-07-22 16:35:29"}]}, {"id": "8ed4f80d-82bb-4159-8ac0-17c0cf0d7c62", "equipment_id": "N-A-68", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "The side rail locking system is broken. \r\nNeed to check with the manufacturer for the availability of spare parts.", "created_at": "2025-07-22 16:53:57", "updated_at": "2025-07-22 16:53:57", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2facd099-76f5-4aab-bb52-1c245024d3d5", "note_id": "8ed4f80d-82bb-4159-8ac0-17c0cf0d7c62", "original_filename": "LOG__1572.pdf", "stored_filename": "4a8afb5a-10b3-4caa-b737-54cb5b3f4116.pdf", "file_path": "app/static/uploads/history/4a8afb5a-10b3-4caa-b737-54cb5b3f4116.pdf", "mime_type": "application/pdf", "file_size": 484320, "upload_date": "2025-07-22 16:53:57"}]}, {"id": "1548c056-6d54-4c95-9e94-e6c82d568fc4", "equipment_id": "18X25721", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Power issue was identified in the fuse section. \r\nThe problem was resolved, and the unit was checked and confirmed to be working properly.", "created_at": "2025-07-22 16:59:23", "updated_at": "2025-07-22 16:59:23", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b8b4da0c-524f-4d61-a24a-959af5c87665", "note_id": "1548c056-6d54-4c95-9e94-e6c82d568fc4", "original_filename": "LOG__1090.pdf", "stored_filename": "2eab97b7-24e8-4d96-98ce-d1e8382d5557.pdf", "file_path": "app/static/uploads/history/2eab97b7-24e8-4d96-98ce-d1e8382d5557.pdf", "mime_type": "application/pdf", "file_size": 522768, "upload_date": "2025-07-22 16:59:23"}]}, {"id": "fc0e363b-e014-49eb-bb60-4123e7c82c8f", "equipment_id": "51070124023", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the intraoral camera with a new one under warranty. \r\nThe device was tested and confirmed to be functioning properly.", "created_at": "2025-07-22 17:02:11", "updated_at": "2025-07-22 17:02:11", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1afa33c1-bab0-4ce5-9bbf-3d4756423ad9", "note_id": "fc0e363b-e014-49eb-bb60-4123e7c82c8f", "original_filename": "LOG__1066.pdf", "stored_filename": "e7d241c1-f1c0-4efe-9c75-b14d136ab218.pdf", "file_path": "app/static/uploads/history/e7d241c1-f1c0-4efe-9c75-b14d136ab218.pdf", "mime_type": "application/pdf", "file_size": 499456, "upload_date": "2025-07-22 17:02:11"}]}, {"id": "4b9c3331-fa63-4eee-b721-f591c1d1ae6f", "equipment_id": "230504", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked and found the clean side door not open.\r\nReset the clean side door relay.\r\none cycle checked completely\r\nThe washer is working well.", "created_at": "2025-07-22 17:05:02", "updated_at": "2025-07-22 17:05:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "dea455fd-b9e7-4055-ace4-a9ffb13d423b", "note_id": "4b9c3331-fa63-4eee-b721-f591c1d1ae6f", "original_filename": "230504_B.pdf", "stored_filename": "f242ac3f-51a9-4d49-9386-052c6dcbc0c7.pdf", "file_path": "app/static/uploads/history/f242ac3f-51a9-4d49-9386-052c6dcbc0c7.pdf", "mime_type": "application/pdf", "file_size": 510960, "upload_date": "2025-07-22 17:05:02"}]}, {"id": "0409cbdf-24c4-4183-9f16-2afabda6a7da", "equipment_id": "226367", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Installed air and water line connections for the sealer unit, sourced from the dental chair. Connections were positioned externally for easy access, allowing convenient connection and disconnection.", "created_at": "2025-07-22 17:07:30", "updated_at": "2025-07-22 17:07:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7eca364d-96d8-419a-9949-0c0735100484", "note_id": "0409cbdf-24c4-4183-9f16-2afabda6a7da", "original_filename": "LOG__1068.pdf", "stored_filename": "105689b8-c224-469f-b8ed-ab21d6d3bc23.pdf", "file_path": "app/static/uploads/history/105689b8-c224-469f-b8ed-ab21d6d3bc23.pdf", "mime_type": "application/pdf", "file_size": 487168, "upload_date": "2025-07-22 17:07:30"}]}, {"id": "43271e28-72a1-43f5-9f74-4ab40b86f55b", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "found that the two valves are faulty.\r\nReplace the two valves with a new one.\r\ntested the unit with a vacuum test, Bowie-Dick test, and cycle.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-22 17:10:15", "updated_at": "2025-07-22 17:10:15", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "edc77ed9-836e-4504-b5f3-87f621196519", "note_id": "43271e28-72a1-43f5-9f74-4ab40b86f55b", "original_filename": "220823.pdf", "stored_filename": "09b07139-b194-4109-9ea6-d3df16c74d37.pdf", "file_path": "app/static/uploads/history/09b07139-b194-4109-9ea6-d3df16c74d37.pdf", "mime_type": "application/pdf", "file_size": 533712, "upload_date": "2025-07-22 17:10:15"}]}, {"id": "c8b5c636-d2ef-495d-a264-1f60a58f4ea3", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and tested one cycle, couldn't find the stain on the packing.\r\nfound that before the cycle, which occurs, the stain has more than a 5 hr. time gap.\r\nThe biomedical engineer recommends doing a pretest cycle before using the machine after a long gap.", "created_at": "2025-07-22 17:14:44", "updated_at": "2025-07-22 17:14:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1e0d30d6-e3fc-41dd-b949-79dec9333cbb", "note_id": "c8b5c636-d2ef-495d-a264-1f60a58f4ea3", "original_filename": "220823_B.pdf", "stored_filename": "9c098af8-b490-423b-a1b2-ac15aa5febd7.pdf", "file_path": "app/static/uploads/history/9c098af8-b490-423b-a1b2-ac15aa5febd7.pdf", "mime_type": "application/pdf", "file_size": 525344, "upload_date": "2025-07-22 17:14:44"}]}, {"id": "55160ab0-daa7-4c22-96da-a0c8230bc18d", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the unloading door is not opening.\r\nManually open the door and reset the relay.\r\nThe unit is working properly.\r\nunit is under observation.", "created_at": "2025-07-22 17:22:53", "updated_at": "2025-07-22 17:22:53", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2da01a29-99a9-49df-b215-795f701c7551", "note_id": "55160ab0-daa7-4c22-96da-a0c8230bc18d", "original_filename": "230506_F.pdf", "stored_filename": "86cec3b4-3635-4799-8905-fbbfa417383f.pdf", "file_path": "app/static/uploads/history/86cec3b4-3635-4799-8905-fbbfa417383f.pdf", "mime_type": "application/pdf", "file_size": 503104, "upload_date": "2025-07-22 17:22:53"}]}, {"id": "41424a9c-c4e7-48ca-81cc-b4f686253c4a", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and cleaned all pipes cleaned the chamber and steam inlet.\r\nperformed a test cycle, and the unit is under observation.", "created_at": "2025-07-22 17:24:28", "updated_at": "2025-07-22 17:24:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1c07b495-6247-4e16-940d-c47b9c6c793f", "note_id": "41424a9c-c4e7-48ca-81cc-b4f686253c4a", "original_filename": "220824.pdf", "stored_filename": "87ab45a6-2766-4ced-85c3-ec8d341233f7.pdf", "file_path": "app/static/uploads/history/87ab45a6-2766-4ced-85c3-ec8d341233f7.pdf", "mime_type": "application/pdf", "file_size": 530016, "upload_date": "2025-07-22 17:24:28"}]}, {"id": "0b68a8f4-10d5-471c-a36e-53c8926eb2c5", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspect the unit and clean all the pipes.\r\nClean the chamber and steam inlet.\r\nperformed a test cycle, and the unit is under observation.", "created_at": "2025-07-22 17:27:28", "updated_at": "2025-07-22 17:27:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "57db4223-239e-44c4-984d-4fc173a4e283", "note_id": "0b68a8f4-10d5-471c-a36e-53c8926eb2c5", "original_filename": "220823_C.pdf", "stored_filename": "58f73d52-8b46-4e5a-bb30-077122a0c54b.pdf", "file_path": "app/static/uploads/history/58f73d52-8b46-4e5a-bb30-077122a0c54b.pdf", "mime_type": "application/pdf", "file_size": 530016, "upload_date": "2025-07-22 17:27:29"}]}, {"id": "fcea93ad-1f42-439e-8f5c-a06a8a5ceea1", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the unloading door is not opening.\r\nOpen the door manually and reset the relay.\r\ndoor is working properly.\r\ndone one cycle, and the door opens properly.\r\nunit is under observation.", "created_at": "2025-07-22 17:30:23", "updated_at": "2025-07-22 17:30:23", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "09f1c0c2-61b6-4d6f-a675-ef4901af016d", "note_id": "fcea93ad-1f42-439e-8f5c-a06a8a5ceea1", "original_filename": "230506_G.pdf", "stored_filename": "e65f7a8e-b228-4b6e-8f8c-89379535b6de.pdf", "file_path": "app/static/uploads/history/e65f7a8e-b228-4b6e-8f8c-89379535b6de.pdf", "mime_type": "application/pdf", "file_size": 533312, "upload_date": "2025-07-22 17:30:23"}]}, {"id": "3667b2f7-6899-49ac-bda8-df2bc8892d7d", "equipment_id": "230504", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Verified all settings.\r\nRun two cycles.\r\nunit is working properly.", "created_at": "2025-07-22 17:34:00", "updated_at": "2025-07-22 17:34:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bb69bc5f-ed96-4d08-b22d-8e05272f2a1d", "note_id": "3667b2f7-6899-49ac-bda8-df2bc8892d7d", "original_filename": "230504_C.pdf", "stored_filename": "b1c70517-e57e-47ff-a509-e675484a52d5.pdf", "file_path": "app/static/uploads/history/b1c70517-e57e-47ff-a509-e675484a52d5.pdf", "mime_type": "application/pdf", "file_size": 508256, "upload_date": "2025-07-22 17:34:00"}]}, {"id": "5749eedf-82fb-46a9-92cc-feee71982aae", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and found the trolley was not inserted properly due to mishandling.\r\nadvice to instruct the user to take care and handle the unit in the proper way.", "created_at": "2025-07-23 06:29:32", "updated_at": "2025-07-23 06:29:32", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "203fc289-e371-4f13-be6c-2e6f760d0100", "note_id": "5749eedf-82fb-46a9-92cc-feee71982aae", "original_filename": "220823_D.pdf", "stored_filename": "c5ee73f9-21da-4bbd-b5d8-9f02cfd745bc.pdf", "file_path": "app/static/uploads/history/c5ee73f9-21da-4bbd-b5d8-9f02cfd745bc.pdf", "mime_type": "application/pdf", "file_size": 552192, "upload_date": "2025-07-23 06:29:32"}]}, {"id": "cb077c94-22fd-4b68-a2cc-dcda3ab4ce92", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the stain occurs after the procedure.\r\nreplaced the drain trap and check valve.\r\ntried some cycles and found that there is no stain occurring.\r\nunit is under observation.", "created_at": "2025-07-23 06:31:58", "updated_at": "2025-07-23 06:31:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d93af216-94fd-417a-a502-3f6778e19e00", "note_id": "cb077c94-22fd-4b68-a2cc-dcda3ab4ce92", "original_filename": "220824_B.pdf", "stored_filename": "26bad2c5-70fe-42bb-a82c-23a485c1a949.pdf", "file_path": "app/static/uploads/history/26bad2c5-70fe-42bb-a82c-23a485c1a949.pdf", "mime_type": "application/pdf", "file_size": 522224, "upload_date": "2025-07-23 06:31:58"}]}, {"id": "8f248a0e-64fd-4ff8-81ea-eda04f300894", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the door is not opening.\r\nManually open the door and reset the error.\r\nchecked the relay, and it's working.\r\ntested one cycle, and now the unit is working properly.", "created_at": "2025-07-23 06:41:59", "updated_at": "2025-07-23 06:41:59", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bb64ec55-aca7-4dcd-a74b-a41cbd999e2c", "note_id": "8f248a0e-64fd-4ff8-81ea-eda04f300894", "original_filename": "230506_H.pdf", "stored_filename": "c6cb1444-b302-44ba-bd25-488bcb881247.pdf", "file_path": "app/static/uploads/history/c6cb1444-b302-44ba-bd25-488bcb881247.pdf", "mime_type": "application/pdf", "file_size": 540976, "upload_date": "2025-07-23 06:41:59"}]}, {"id": "2d99fd4e-0c64-40ee-83b0-a458d75f6728", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and found that both cold waters are not present.\r\nchecked on the top and found that both chillers are not working and there is hot water in the tank.\r\nSolve the problem with the chillers for the proper working of the autoclave.", "created_at": "2025-07-23 06:49:44", "updated_at": "2025-07-23 06:49:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0544273b-c40c-488d-b95a-6cd1f4b4aff0", "note_id": "2d99fd4e-0c64-40ee-83b0-a458d75f6728", "original_filename": "220824_C.pdf", "stored_filename": "5fb8a3c4-c681-4621-8064-cc76a7a23e26.pdf", "file_path": "app/static/uploads/history/5fb8a3c4-c681-4621-8064-cc76a7a23e26.pdf", "mime_type": "application/pdf", "file_size": 581888, "upload_date": "2025-07-23 06:49:44"}]}, {"id": "62b60a93-7e26-472b-9be1-a1ff08cca3da", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the drainage is blocked with the impurities in the steam generator.\r\ncleared the steam generator and fixed it properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-23 06:52:28", "updated_at": "2025-07-23 06:52:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "58aa7c33-c9f9-4945-92ed-c34ca04c27ca", "note_id": "62b60a93-7e26-472b-9be1-a1ff08cca3da", "original_filename": "220824_D.pdf", "stored_filename": "9f97c0fb-5c7a-4730-bc47-070e1d757ebb.pdf", "file_path": "app/static/uploads/history/9f97c0fb-5c7a-4730-bc47-070e1d757ebb.pdf", "mime_type": "application/pdf", "file_size": 539376, "upload_date": "2025-07-23 06:52:29"}]}, {"id": "a4e0c0e5-8e31-4fd5-bdeb-b43928d8c5a9", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the Bowie-Dick test failed.\r\nchecked the cooling water temperature and found that it's above 50 degrees.\r\ninspected the cooling tank and found that the water level is very low, and it's the cause of the high temperature for cooling water. Without proper cooling water, the machine will not work properly.\r\ninformed maintenance team to solve the issue.", "created_at": "2025-07-23 07:05:02", "updated_at": "2025-07-23 07:05:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "61dee152-4479-4876-bc7e-473d7df0c684", "note_id": "a4e0c0e5-8e31-4fd5-bdeb-b43928d8c5a9", "original_filename": "220824_E.pdf", "stored_filename": "56c3b088-0583-4fe7-824b-c069ca7081b0.pdf", "file_path": "app/static/uploads/history/56c3b088-0583-4fe7-824b-c069ca7081b0.pdf", "mime_type": "application/pdf", "file_size": 582480, "upload_date": "2025-07-23 07:05:02"}]}, {"id": "fbc8207e-2666-42d9-9f61-9496a78eead4", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the unit is not occurring proper vacuum.\r\nadjusted the anti-cavitation valve.\r\nunit is working properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-23 07:07:21", "updated_at": "2025-07-23 07:07:21", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "02e18bfc-c6a9-4a3c-9ea3-3c6c2f7d90a2", "note_id": "fbc8207e-2666-42d9-9f61-9496a78eead4", "original_filename": "220823_E.pdf", "stored_filename": "b0824787-7c6f-4539-a8af-633dd0c40d08.pdf", "file_path": "app/static/uploads/history/b0824787-7c6f-4539-a8af-633dd0c40d08.pdf", "mime_type": "application/pdf", "file_size": 535648, "upload_date": "2025-07-23 07:07:21"}]}, {"id": "9fa589a3-5dc2-40f3-bea1-5739e924cd56", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and cleaned the steam generator using a pressure pump.\r\nfound that the impurities settle down in the steam generator.\r\nThe unit now is working properly.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-23 07:10:13", "updated_at": "2025-07-23 07:10:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "80bd5e4e-fea6-4e92-b7d2-1879af489356", "note_id": "9fa589a3-5dc2-40f3-bea1-5739e924cd56", "original_filename": "220823_F.pdf", "stored_filename": "6d823708-7799-42c6-8564-ca67075ee6ad.pdf", "file_path": "app/static/uploads/history/6d823708-7799-42c6-8564-ca67075ee6ad.pdf", "mime_type": "application/pdf", "file_size": 544624, "upload_date": "2025-07-23 07:10:13"}]}, {"id": "7f37d535-414b-4b8c-9e1a-9cf232a70c50", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the unloading door is not opening after the cycle.\r\nrepaired the unit and tested the relay module for the door.\r\nCurrently the door is opening, and the unit is under observation.", "created_at": "2025-07-23 07:12:47", "updated_at": "2025-07-23 07:12:47", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e779f7d6-0058-4af7-b26e-ea2385271bbe", "note_id": "7f37d535-414b-4b8c-9e1a-9cf232a70c50", "original_filename": "230506_I.pdf", "stored_filename": "c639542f-49ba-4426-8dfe-5cfca3346497.pdf", "file_path": "app/static/uploads/history/c639542f-49ba-4426-8dfe-5cfca3346497.pdf", "mime_type": "application/pdf", "file_size": 545760, "upload_date": "2025-07-23 07:12:47"}]}, {"id": "54fcd485-a35f-458d-885b-d3545ba0fd91", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and cleaned the chamber.\r\nDrain out the steam generator water and refill the room water.\r\nThe occurrence is due to the water quality.\r\nunit is under observation.", "created_at": "2025-07-23 07:16:32", "updated_at": "2025-07-23 07:16:32", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7530b30e-9508-46d9-be0b-c16168cd7c8b", "note_id": "54fcd485-a35f-458d-885b-d3545ba0fd91", "original_filename": "220823_G.pdf", "stored_filename": "9f1ec91b-8cf3-4060-893f-c2b3c3e38888.pdf", "file_path": "app/static/uploads/history/9f1ec91b-8cf3-4060-893f-c2b3c3e38888.pdf", "mime_type": "application/pdf", "file_size": 537488, "upload_date": "2025-07-23 07:16:32"}]}, {"id": "d385dfc5-2d2f-43c2-b5db-91c53b44cdb8", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "unit cleaned the chamber and rack.\r\nClean the room water tank and refill it with new water.\r\ntested one normal cycle and confirmed the unit is working properly.\r\n unit is under observation for one week.\r\nstain will occur again; need to test the water softener water quality.", "created_at": "2025-07-23 07:23:52", "updated_at": "2025-07-23 07:23:52", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0139fc17-c408-4684-9d9e-94ca76980a67", "note_id": "d385dfc5-2d2f-43c2-b5db-91c53b44cdb8", "original_filename": "220823_H.pdf", "stored_filename": "e5f28c70-a6fb-4efe-ad01-c46fa7f199ba.pdf", "file_path": "app/static/uploads/history/e5f28c70-a6fb-4efe-ad01-c46fa7f199ba.pdf", "mime_type": "application/pdf", "file_size": 548768, "upload_date": "2025-07-23 07:23:52"}]}, {"id": "6cd6303a-84ed-48ed-bcde-deed51f1ee00", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that there is no cool water supply to the machine.\r\nchecked the chiller side tank and found that the one motor is tripped off.\r\nRestart the motor and check the water flow.\r\nThe cool water supply is okay.\r\nperformed one cycle and found that the unit is working properly.\r\ninformed the maintenance team to check the motor and chiller regularly for better performance of the machine.", "created_at": "2025-07-23 07:27:18", "updated_at": "2025-07-23 07:27:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b3fe90f8-2248-49a0-969a-6bc2008d485b", "note_id": "6cd6303a-84ed-48ed-bcde-deed51f1ee00", "original_filename": "220823_I.pdf", "stored_filename": "98e1acb4-8288-410d-8f5d-806ebda5f71b.pdf", "file_path": "app/static/uploads/history/98e1acb4-8288-410d-8f5d-806ebda5f71b.pdf", "mime_type": "application/pdf", "file_size": 565264, "upload_date": "2025-07-23 07:27:18"}]}, {"id": "af891c75-5654-4729-b54b-2c417096dc6a", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the cooling water temperature is too high.\r\nchecked the chiller unit and found that the unit is not working; without the chiller, the two autoclaves are not working properly.\r\ninformed the maintenance team to fix the issue.", "created_at": "2025-07-23 07:29:40", "updated_at": "2025-07-23 07:29:40", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "dd9f75e4-ca60-4133-85d2-1da386975e2c", "note_id": "af891c75-5654-4729-b54b-2c417096dc6a", "original_filename": "220823_J.pdf", "stored_filename": "c678fa65-aa8b-4814-936f-c0949ecd5c51.pdf", "file_path": "app/static/uploads/history/c678fa65-aa8b-4814-936f-c0949ecd5c51.pdf", "mime_type": "application/pdf", "file_size": 552624, "upload_date": "2025-07-23 07:29:40"}]}, {"id": "ab6beeff-916f-42a2-96a1-3e13f0d63825", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspecting the unit and suspecting the issue is due to the water quality for the steam generator.\r\ncleaned the chamber using sandpaper to remove the settled particles.\r\nThe water softener needs servicing, and it's changeable.\r\nThe unit is working; only the steam generator water quality is the issue.", "created_at": "2025-07-23 07:32:35", "updated_at": "2025-07-23 07:32:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b957e557-bd1e-4888-82fe-2541fb844faf", "note_id": "ab6beeff-916f-42a2-96a1-3e13f0d63825", "original_filename": "220823_K.pdf", "stored_filename": "6a736140-b257-451c-9cae-a61f1ba2a21e.pdf", "file_path": "app/static/uploads/history/6a736140-b257-451c-9cae-a61f1ba2a21e.pdf", "mime_type": "application/pdf", "file_size": 557712, "upload_date": "2025-07-23 07:32:35"}]}, {"id": "50059b8e-68d8-476d-8ac5-3c9783eb3a7d", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the cold water from the chiller is not cool.\r\nThe high temperature of cold water affects the vacuum.\r\nNeed to upgrade the chiller for proper working of the machine.", "created_at": "2025-07-23 08:08:30", "updated_at": "2025-07-23 08:08:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bdabd063-6a26-4a1b-9c8f-4c8e73b04144", "note_id": "50059b8e-68d8-476d-8ac5-3c9783eb3a7d", "original_filename": "220823_L.pdf", "stored_filename": "75ed039e-9313-4acc-a9de-a5580bc7845d.pdf", "file_path": "app/static/uploads/history/75ed039e-9313-4acc-a9de-a5580bc7845d.pdf", "mime_type": "application/pdf", "file_size": 517712, "upload_date": "2025-07-23 08:08:31"}]}, {"id": "f805b7b2-c139-47d5-93ea-656ff4d7db58", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected the unit, checked all connections, but couldn't find the problem.\r\ntried two cycles and examined the unit; everything is in limit.\r\nchecked and confirmed that the unit is working properly.", "created_at": "2025-07-23 08:13:12", "updated_at": "2025-07-23 08:13:12", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "6ec6bb9c-a843-407e-8609-74e730c0a381", "note_id": "f805b7b2-c139-47d5-93ea-656ff4d7db58", "original_filename": "220824_F.pdf", "stored_filename": "fa34919f-d5db-4e6d-ad37-89eef72d8f76.pdf", "file_path": "app/static/uploads/history/fa34919f-d5db-4e6d-ad37-89eef72d8f76.pdf", "mime_type": "application/pdf", "file_size": 531808, "upload_date": "2025-07-23 08:13:12"}]}, {"id": "297f6740-3da7-48a7-ac63-19762ddbcabe", "equipment_id": "230504", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance and annual maintenance kit replacement were performed for the unit.\r\nchecked and confirmed that the unit is working in good condition.", "created_at": "2025-07-23 08:15:25", "updated_at": "2025-07-23 08:15:25", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1d437cc9-b770-4711-9d49-42a412171b7b", "note_id": "297f6740-3da7-48a7-ac63-19762ddbcabe", "original_filename": "230504_D.pdf", "stored_filename": "bfcca070-2b5e-4a5f-8f1a-290b938601eb.pdf", "file_path": "app/static/uploads/history/bfcca070-2b5e-4a5f-8f1a-290b938601eb.pdf", "mime_type": "application/pdf", "file_size": 520448, "upload_date": "2025-07-23 08:15:25"}]}, {"id": "4f37caa4-f2de-4222-a622-cd85113e5b01", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance and annual maintenance kit replacement were performed for the unit.\r\nchecked and confirmed that the unit is working in good condition.", "created_at": "2025-07-23 08:17:24", "updated_at": "2025-07-23 08:17:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ff4c6155-a076-4db6-8ab9-15a707bdf9ea", "note_id": "4f37caa4-f2de-4222-a622-cd85113e5b01", "original_filename": "230506_J.pdf", "stored_filename": "ab6e52cb-e892-41bb-86f3-37eb907a3b1d.pdf", "file_path": "app/static/uploads/history/ab6e52cb-e892-41bb-86f3-37eb907a3b1d.pdf", "mime_type": "application/pdf", "file_size": 520448, "upload_date": "2025-07-23 08:17:24"}]}, {"id": "428c0231-958c-4f7f-beeb-05dff669c69e", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance and annual maintenance kit replacement were performed for the units.\r\nchecked and confirmed the unit is working in good condition.", "created_at": "2025-07-23 08:19:20", "updated_at": "2025-07-23 08:19:21", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "cce009a6-bdbd-42ce-bf6a-8aa51fbe4e7f", "note_id": "428c0231-958c-4f7f-beeb-05dff669c69e", "original_filename": "220823_M.pdf", "stored_filename": "14878bda-3939-46fd-b430-b5baeb1af807.pdf", "file_path": "app/static/uploads/history/14878bda-3939-46fd-b430-b5baeb1af807.pdf", "mime_type": "application/pdf", "file_size": 521040, "upload_date": "2025-07-23 08:19:21"}]}, {"id": "60c2ccf7-5a66-470d-9a15-71d8dd7d8c47", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance and annual maintenance kit replacement were performed in the unit.\r\nchecked and confirmed that the unit is working in good condition.", "created_at": "2025-07-23 08:21:54", "updated_at": "2025-07-23 08:21:54", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5476f06d-5d7e-4f7d-9b2d-204d943f1638", "note_id": "60c2ccf7-5a66-470d-9a15-71d8dd7d8c47", "original_filename": "220824_G.pdf", "stored_filename": "db0b7fec-8c8d-4825-87b9-d7332ca1bb86.pdf", "file_path": "app/static/uploads/history/db0b7fec-8c8d-4825-87b9-d7332ca1bb86.pdf", "mime_type": "application/pdf", "file_size": 521040, "upload_date": "2025-07-23 08:21:54"}]}, {"id": "183bb621-f43a-41dc-8e74-bb4a96ce163e", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that there is no cool water supply for the unit and the error happened.\r\nInform the maintenance team and solve the cool water supply.\r\nunit is working properly.", "created_at": "2025-07-23 08:25:18", "updated_at": "2025-07-23 08:25:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e17e5f61-ec4f-41b5-b476-bf556d1b2760", "note_id": "183bb621-f43a-41dc-8e74-bb4a96ce163e", "original_filename": "220823_N.pdf", "stored_filename": "6e72fee7-41c0-40ea-a2be-384da93fedbf.pdf", "file_path": "app/static/uploads/history/6e72fee7-41c0-40ea-a2be-384da93fedbf.pdf", "mime_type": "application/pdf", "file_size": 547216, "upload_date": "2025-07-23 08:25:18"}]}, {"id": "ca3c7694-f357-44fc-bc03-6e9b685b4e77", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit, and there is no cool water supply, and the error happened.\r\nInform the maintenance team, and they solve the cool water supply.\r\nThe unit is working in good condition.", "created_at": "2025-07-23 08:27:44", "updated_at": "2025-07-23 08:27:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a1b75223-8627-40e0-83a7-3ddcf127ef0a", "note_id": "ca3c7694-f357-44fc-bc03-6e9b685b4e77", "original_filename": "220824_H.pdf", "stored_filename": "ae54a393-d52c-43d6-8c9a-6eebf8f9ab52.pdf", "file_path": "app/static/uploads/history/ae54a393-d52c-43d6-8c9a-6eebf8f9ab52.pdf", "mime_type": "application/pdf", "file_size": 547216, "upload_date": "2025-07-23 08:27:45"}]}, {"id": "859b4610-583e-42ce-a282-b1b46680122e", "equipment_id": "7295", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the thermostat is faulty.\r\nReplace it with a new one.\r\nunit is under observation.\r\nunit is working properly.", "created_at": "2025-07-23 08:29:34", "updated_at": "2025-07-23 08:29:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ab2fa631-e953-4c11-b5c0-5cafd5d04173", "note_id": "859b4610-583e-42ce-a282-b1b46680122e", "original_filename": "7295.pdf", "stored_filename": "3612c047-ff70-4fad-9068-8362043d5d9c.pdf", "file_path": "app/static/uploads/history/3612c047-ff70-4fad-9068-8362043d5d9c.pdf", "mime_type": "application/pdf", "file_size": 523072, "upload_date": "2025-07-23 08:29:34"}]}, {"id": "2484c5f1-b4a2-4ffa-a9cc-ec6469653de7", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unloading door time exceeded error.\r\nfixed the problem and tested the full cycle.\r\nunit is working okay.", "created_at": "2025-07-23 08:32:34", "updated_at": "2025-07-23 08:32:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "92674549-3215-429f-861e-6103894c3069", "note_id": "2484c5f1-b4a2-4ffa-a9cc-ec6469653de7", "original_filename": "230506_K.pdf", "stored_filename": "31fe1507-c5d8-485f-b7c7-410dbc19cf08.pdf", "file_path": "app/static/uploads/history/31fe1507-c5d8-485f-b7c7-410dbc19cf08.pdf", "mime_type": "application/pdf", "file_size": 458576, "upload_date": "2025-07-23 08:32:34"}]}, {"id": "2b62528f-d142-420d-bd52-25067d5ce7f9", "equipment_id": "230504", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced the six water filters with new ones.\r\nunit is working properly.", "created_at": "2025-07-23 08:34:53", "updated_at": "2025-07-23 08:34:53", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bab8d083-a17c-4317-9608-669bcf59d314", "note_id": "2b62528f-d142-420d-bd52-25067d5ce7f9", "original_filename": "230504_E.pdf", "stored_filename": "1d98f6ae-28c1-4516-a414-15956ee98af4.pdf", "file_path": "app/static/uploads/history/1d98f6ae-28c1-4516-a414-15956ee98af4.pdf", "mime_type": "application/pdf", "file_size": 455072, "upload_date": "2025-07-23 08:34:53"}]}, {"id": "d749e08a-af57-4ff9-be13-65b223b0cbbe", "equipment_id": "220824", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "the cycle before inspecting the unit, having an error with the cooling water temperature being high.\r\nchecked the input water supply, and the temperature is in limit.\r\nAt the time of the cycle, the water supply is interrupted.\r\ntried a new cycle on the unit and found that the unit is working properly.", "created_at": "2025-07-23 08:40:35", "updated_at": "2025-07-23 08:40:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7f0cb42a-52d7-475b-8fb1-8492a44533e2", "note_id": "d749e08a-af57-4ff9-be13-65b223b0cbbe", "original_filename": "220824_I.pdf", "stored_filename": "b97c84f5-caa0-417b-bd0f-28002f890902.pdf", "file_path": "app/static/uploads/history/b97c84f5-caa0-417b-bd0f-28002f890902.pdf", "mime_type": "application/pdf", "file_size": 500672, "upload_date": "2025-07-23 08:40:35"}]}, {"id": "0f0c3eab-7363-491f-99bf-031e68850bfe", "equipment_id": "220823", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "the cycle before inspecting the unit, having an error with the cooling water temperature being high.\r\nchecked the input water supply, and the temperature is in limit.\r\nAt the time of the cycle, the water supply is interrupted.\r\ntried a new cycle on the unit and found that the unit is working properly.", "created_at": "2025-07-23 08:41:43", "updated_at": "2025-07-23 08:41:43", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f1af880e-9efe-4bff-8f08-10fa4ffa5e33", "note_id": "0f0c3eab-7363-491f-99bf-031e68850bfe", "original_filename": "220823_O.pdf", "stored_filename": "78aa5cea-5d7f-4616-ac27-30f69985c472.pdf", "file_path": "app/static/uploads/history/78aa5cea-5d7f-4616-ac27-30f69985c472.pdf", "mime_type": "application/pdf", "file_size": 500672, "upload_date": "2025-07-23 08:41:43"}]}, {"id": "868fb13c-7b59-4ab4-bc50-a13a54cdb9d3", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspect the unit; error 91 occurs.\r\nchecked the unit and found the heating element issue.\r\nreset the heating element and tried.\r\nunit is working properly.", "created_at": "2025-07-23 08:44:04", "updated_at": "2025-07-23 08:44:04", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "28f4f680-bb30-4009-b9b3-bfbd902b5656", "note_id": "868fb13c-7b59-4ab4-bc50-a13a54cdb9d3", "original_filename": "230506_L.pdf", "stored_filename": "7704cbf0-0bdf-4aa5-a395-bcc75121d249.pdf", "file_path": "app/static/uploads/history/7704cbf0-0bdf-4aa5-a395-bcc75121d249.pdf", "mime_type": "application/pdf", "file_size": 471952, "upload_date": "2025-07-23 08:44:04"}]}, {"id": "ae34059d-46c0-4295-bf1b-5ea7f0ae88e9", "equipment_id": "230506", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspect the unit, and error 91 occurs.\r\nchecked the heating element and reset the heating element.\r\nerror is cleared. \r\nunit is under observation.\r\nunit is working properly.", "created_at": "2025-07-23 08:46:18", "updated_at": "2025-07-23 08:46:18", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "adbaf34a-d4db-4d3b-b026-e51dbd206de6", "note_id": "ae34059d-46c0-4295-bf1b-5ea7f0ae88e9", "original_filename": "230506_M.pdf", "stored_filename": "84bd2077-85b5-4234-b0af-89ff0acae476.pdf", "file_path": "app/static/uploads/history/84bd2077-85b5-4234-b0af-89ff0acae476.pdf", "mime_type": "application/pdf", "file_size": 474416, "upload_date": "2025-07-23 08:46:18"}]}, {"id": "88082997-b5f8-4a4d-89f5-3dfadde5d416", "equipment_id": "230504", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the unloading door is jammed due to the sponge from the bottom.\r\nPart of the chamber falls down and blocks the door movement.\r\nfixed that the sponge on the chamber\r\ndoo is working properly.", "created_at": "2025-07-23 08:59:27", "updated_at": "2025-07-23 08:59:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fbaa8a7f-453b-48d0-9448-6c4e0118687c", "note_id": "88082997-b5f8-4a4d-89f5-3dfadde5d416", "original_filename": "230504_F.pdf", "stored_filename": "24ecc6fd-02e2-4b47-9c9d-6ef795ecc0b3.pdf", "file_path": "app/static/uploads/history/24ecc6fd-02e2-4b47-9c9d-6ef795ecc0b3.pdf", "mime_type": "application/pdf", "file_size": 497408, "upload_date": "2025-07-23 08:59:27"}]}, {"id": "5ef2dd12-01c8-47f4-90a1-b6fa23d21bd3", "equipment_id": "17866", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked and cleaned the instrument and internal parts.\r\nFlush the reaction chamber with cell clean manually.\r\ndrained reaction chambers and waste fluid chambers.\r\nThe transducer was cleaned by brush, and RBC clogs were removed.\r\nPerform rinse flow cell and air bubble removal.\r\ncleaned the filter and checked the FCM syringe, tubes, and sensors.\r\nRun QC passed and checked.", "created_at": "2025-07-23 09:13:28", "updated_at": "2025-07-23 09:13:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "6c4c82c4-8bac-4cb9-97af-3ad5b867df0b", "note_id": "5ef2dd12-01c8-47f4-90a1-b6fa23d21bd3", "original_filename": "17866_B.pdf", "stored_filename": "b54bd3a3-ba09-4663-b159-4692559d4877.pdf", "file_path": "app/static/uploads/history/b54bd3a3-ba09-4663-b159-4692559d4877.pdf", "mime_type": "application/pdf", "file_size": 563920, "upload_date": "2025-07-23 09:13:28"}]}, {"id": "36503583-0d81-4f16-9db5-adf494e087bd", "equipment_id": "11723714K", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the unit is not heating.\r\nrepaired and reset the thermostat switch.\r\nunit is working properly.", "created_at": "2025-07-23 09:15:37", "updated_at": "2025-07-23 09:15:37", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "430ccede-a78a-4ee6-910d-011a0aedf650", "note_id": "36503583-0d81-4f16-9db5-adf494e087bd", "original_filename": "11723714K.pdf", "stored_filename": "2ab25f59-b0c0-4180-8214-2bd616cc3e64.pdf", "file_path": "app/static/uploads/history/2ab25f59-b0c0-4180-8214-2bd616cc3e64.pdf", "mime_type": "application/pdf", "file_size": 526160, "upload_date": "2025-07-23 09:15:37"}]}, {"id": "590bfa47-78f0-4bc4-b392-5230938da96e", "equipment_id": "60150147", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The lid lock and main power transformer are faulty. \r\nneed replacement.\r\nGive a quotation for repairs.", "created_at": "2025-07-23 09:19:13", "updated_at": "2025-07-23 09:19:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "6ffee760-8b0c-430e-8a53-b736a7351c9a", "note_id": "590bfa47-78f0-4bc4-b392-5230938da96e", "original_filename": "Log_no._1770.pdf", "stored_filename": "0d749e3f-ac0e-4e5d-91ba-6aed11a59cb6.pdf", "file_path": "app/static/uploads/history/0d749e3f-ac0e-4e5d-91ba-6aed11a59cb6.pdf", "mime_type": "application/pdf", "file_size": 518976, "upload_date": "2025-07-23 09:19:13"}]}, {"id": "6214b5fc-6b56-4222-b120-808f3e7bfbe2", "equipment_id": "18087047", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "defrosted system, cleaned condem, and serviced.\r\nunit is working properly.", "created_at": "2025-07-23 09:24:12", "updated_at": "2025-07-23 09:24:12", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f9672669-afbb-4ee1-9911-b220951bbb85", "note_id": "6214b5fc-6b56-4222-b120-808f3e7bfbe2", "original_filename": "18087047.pdf", "stored_filename": "0303c1ae-a9e7-4b29-9481-addf97fc3cc5.pdf", "file_path": "app/static/uploads/history/0303c1ae-a9e7-4b29-9481-addf97fc3cc5.pdf", "mime_type": "application/pdf", "file_size": 511136, "upload_date": "2025-07-23 09:24:12"}]}, {"id": "cda1ba6e-b92b-40ae-87f4-a4bd9f71c10f", "equipment_id": "FF0920DSK0221", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replace the circulation fan with a new one.\r\nchecked and found that the unit is working properly.", "created_at": "2025-07-23 09:25:45", "updated_at": "2025-07-23 09:25:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "db2b873b-6f4a-455c-8483-05d0a9e9f9d5", "note_id": "cda1ba6e-b92b-40ae-87f4-a4bd9f71c10f", "original_filename": "FF0920DSK0221.pdf", "stored_filename": "0ea8951c-c3dc-437c-b055-d160f37d4dbb.pdf", "file_path": "app/static/uploads/history/0ea8951c-c3dc-437c-b055-d160f37d4dbb.pdf", "mime_type": "application/pdf", "file_size": 527648, "upload_date": "2025-07-23 09:25:45"}]}, {"id": "44dab72d-96c8-4cc4-969a-ccaff998133c", "equipment_id": "601900023", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and found the damaged lid lock.\r\nneed a replacement for proper working.", "created_at": "2025-07-23 09:27:43", "updated_at": "2025-07-23 09:27:43", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5a0c3a75-86bb-4ff8-bd33-57973547cb5a", "note_id": "44dab72d-96c8-4cc4-969a-ccaff998133c", "original_filename": "Log_no._1735.pdf", "stored_filename": "d2a67f43-9dc6-45e5-b0b6-783fd30d70c3.pdf", "file_path": "app/static/uploads/history/d2a67f43-9dc6-45e5-b0b6-783fd30d70c3.pdf", "mime_type": "application/pdf", "file_size": 534336, "upload_date": "2025-07-23 09:27:43"}]}, {"id": "cf601249-e841-4aca-b02b-5018cda9fc82", "equipment_id": "(21)22E4-03", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "carried out preventive maintenance using maintenance kit 1 for C 302.", "created_at": "2025-07-23 09:31:40", "updated_at": "2025-07-23 09:31:40", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "24a5fbba-1a46-4a5d-a273-9d1b93ddcf5a", "note_id": "cf601249-e841-4aca-b02b-5018cda9fc82", "original_filename": "22E4-03.pdf", "stored_filename": "ebb841d2-72e6-47da-af79-17fc3a03a198.pdf", "file_path": "app/static/uploads/history/ebb841d2-72e6-47da-af79-17fc3a03a198.pdf", "mime_type": "application/pdf", "file_size": 374078, "upload_date": "2025-07-23 09:31:40"}]}, {"id": "c6787da8-5e2a-46af-aca1-5201b8462fed", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "guided the customer by audio and video calls to clean the T & D disc and perform wash cycles.\r\nmachine working.", "created_at": "2025-07-23 09:34:48", "updated_at": "2025-07-23 09:34:48", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b7ac1a63-d66c-4140-91b6-f566c6434998", "note_id": "c6787da8-5e2a-46af-aca1-5201b8462fed", "original_filename": "19239_F.pdf", "stored_filename": "ba86a47d-65d3-4778-b7d0-828bc84f2ae3.pdf", "file_path": "app/static/uploads/history/ba86a47d-65d3-4778-b7d0-828bc84f2ae3.pdf", "mime_type": "application/pdf", "file_size": 371247, "upload_date": "2025-07-23 09:34:48"}]}, {"id": "34d9cc5d-e8df-42e0-86e9-3254bbd5bc2f", "equipment_id": "(21)22E4-02", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "carried out preventive maintenance \r\ntested system okay.", "created_at": "2025-07-23 09:38:24", "updated_at": "2025-07-23 09:38:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fa60b7b6-dc08-42b2-ac85-fd3053148587", "note_id": "34d9cc5d-e8df-42e0-86e9-3254bbd5bc2f", "original_filename": "22E4-02.pdf", "stored_filename": "1b018518-347f-4b8d-9415-dca000cf0ef7.pdf", "file_path": "app/static/uploads/history/1b018518-347f-4b8d-9415-dca000cf0ef7.pdf", "mime_type": "application/pdf", "file_size": 272096, "upload_date": "2025-07-23 09:38:24"}]}, {"id": "9486e548-8f5a-4582-86dd-76b00bf97398", "equipment_id": "601900023", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the power switch is faulty.\r\nreplaced the power switch with a new one.\r\nunit is working properly.", "created_at": "2025-07-23 09:55:09", "updated_at": "2025-07-23 09:55:09", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b32a7c0f-39ad-4cf2-a278-7040250cd6c3", "note_id": "9486e548-8f5a-4582-86dd-76b00bf97398", "original_filename": "601900023.pdf", "stored_filename": "d744032d-f9f2-4dff-b6f7-7c05f6bc393d.pdf", "file_path": "app/static/uploads/history/d744032d-f9f2-4dff-b6f7-7c05f6bc393d.pdf", "mime_type": "application/pdf", "file_size": 516000, "upload_date": "2025-07-23 09:55:09"}]}, {"id": "0c44a9e9-cae9-4c63-8f0b-9c7f89da4454", "equipment_id": "FF0920DSK0221", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue reported: no cooling\r\nchecked the system, refilled the gas, and replaced the filter.\r\nChecked operation and found it okay.", "created_at": "2025-07-23 14:14:49", "updated_at": "2025-07-23 14:14:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "33ccc35f-2806-43b2-b0c7-5a8aae25f381", "note_id": "0c44a9e9-cae9-4c63-8f0b-9c7f89da4454", "original_filename": "FF0920DSK0221_B.pdf", "stored_filename": "cf956b75-a529-46fc-841d-618a206cde2e.pdf", "file_path": "app/static/uploads/history/cf956b75-a529-46fc-841d-618a206cde2e.pdf", "mime_type": "application/pdf", "file_size": 503968, "upload_date": "2025-07-23 14:14:49"}]}, {"id": "e72ad1a9-07be-4b75-9225-47edd129d3cc", "equipment_id": "FF0920DSK0221", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the compressor gas is low.\r\nrefilled the gas and checked the unit.\r\nThe unit temperature is at the set point.\r\nkept the unit under observation.", "created_at": "2025-07-23 14:19:42", "updated_at": "2025-07-23 14:19:42", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "04160d24-8854-4d3f-bd19-d9208074ebab", "note_id": "e72ad1a9-07be-4b75-9225-47edd129d3cc", "original_filename": "FF0920DSK0221_C.pdf", "stored_filename": "55ee6665-a97b-4874-a55a-54d23b4af438.pdf", "file_path": "app/static/uploads/history/55ee6665-a97b-4874-a55a-54d23b4af438.pdf", "mime_type": "application/pdf", "file_size": 514992, "upload_date": "2025-07-23 14:19:42"}]}, {"id": "cd917db0-2bf3-4271-b72b-adf1fd383b01", "equipment_id": "601900343", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found some paper pieces inside the motor parts.\r\nremoved the papers and checked the unit.\r\nunit is working properly.", "created_at": "2025-07-23 14:24:42", "updated_at": "2025-07-23 14:24:43", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4ce46505-6c96-41dc-9f41-1df147ddec79", "note_id": "cd917db0-2bf3-4271-b72b-adf1fd383b01", "original_filename": "Log_no._1742.pdf", "stored_filename": "04b324ed-a407-46f1-9457-6a70d601f438.pdf", "file_path": "app/static/uploads/history/04b324ed-a407-46f1-9457-6a70d601f438.pdf", "mime_type": "application/pdf", "file_size": 495200, "upload_date": "2025-07-23 14:24:43"}]}, {"id": "a8255508-3781-43e2-a5d7-83f27a80e401", "equipment_id": "FF0920DSK0221", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected the unit and found that the refrigerator temperature is not reaching the set point.\r\nThe unit has one error alarm, ALO3. \r\nNeed to check with the manufacturer regarding the issue.\r\nunit is not in limit.", "created_at": "2025-07-23 14:28:51", "updated_at": "2025-07-23 14:28:51", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d7fe33dc-1629-43d4-ba8c-1dd82ec94673", "note_id": "a8255508-3781-43e2-a5d7-83f27a80e401", "original_filename": "FF0920DSK221_D.pdf", "stored_filename": "5bf8dc30-f5d9-4342-aadd-08bba8c05118.pdf", "file_path": "app/static/uploads/history/5bf8dc30-f5d9-4342-aadd-08bba8c05118.pdf", "mime_type": "application/pdf", "file_size": 527728, "upload_date": "2025-07-23 14:28:51"}]}, {"id": "d2ace88a-f699-4067-a52c-85bf5a6203f4", "equipment_id": "601900023", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The lid lock is broken.\r\nneed to change.\r\nwaiting for the quotation after repairing the lid lock if necessary.", "created_at": "2025-07-23 14:47:35", "updated_at": "2025-07-23 14:47:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "29712da2-ecb7-4688-a9ac-0eec779eba89", "note_id": "d2ace88a-f699-4067-a52c-85bf5a6203f4", "original_filename": "601900023_B.pdf", "stored_filename": "80c127b1-8502-4079-9ab9-3d1b773849fd.pdf", "file_path": "app/static/uploads/history/80c127b1-8502-4079-9ab9-3d1b773849fd.pdf", "mime_type": "application/pdf", "file_size": 482896, "upload_date": "2025-07-23 14:47:35"}]}, {"id": "580a44af-df3d-40b3-b694-a3acd3233bec", "equipment_id": "X-10VL16M10007", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and found that the firewall is blocking the unit IP address.\r\nWith the help of IT, the firewall issue was resolved.\r\nunit is connected to the PC.\r\nunit is working properly.", "created_at": "2025-07-23 14:51:29", "updated_at": "2025-07-23 14:51:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e689b7ac-0f47-469f-a94d-d62284c303ca", "note_id": "580a44af-df3d-40b3-b694-a3acd3233bec", "original_filename": "X-10VL16M10007.pdf", "stored_filename": "68b5cd71-a7b4-4e8b-accf-a656337b140d.pdf", "file_path": "app/static/uploads/history/68b5cd71-a7b4-4e8b-accf-a656337b140d.pdf", "mime_type": "application/pdf", "file_size": 491648, "upload_date": "2025-07-23 14:51:29"}]}, {"id": "086d0973-1f47-4899-8af8-c23cb665c9d9", "equipment_id": "59412", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replace the old UPS with a new one.\r\nconfigured and reset.\r\nunit is working in good condition.\r\nCheckup and backup done successfully.", "created_at": "2025-07-23 14:58:02", "updated_at": "2025-07-23 14:58:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d1c23c93-fcf0-491f-8436-e3eda07a0007", "note_id": "086d0973-1f47-4899-8af8-c23cb665c9d9", "original_filename": "59412.pdf", "stored_filename": "39f339e8-ee9b-49ba-a4ec-dd43c2e5cc14.pdf", "file_path": "app/static/uploads/history/39f339e8-ee9b-49ba-a4ec-dd43c2e5cc14.pdf", "mime_type": "application/pdf", "file_size": 513520, "upload_date": "2025-07-23 14:58:02"}]}, {"id": "1b1c0459-cea4-427a-b6a1-0acce89001e3", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance was done.\r\nsystem functions checked.\r\nSystem backup taken.\r\nHead coil quality assurance done.\r\nsystem is tested and given in working condition.", "created_at": "2025-07-23 15:03:32", "updated_at": "2025-07-23 15:03:32", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9f810065-8ab0-4a8e-9e3b-0e111017cf9f", "note_id": "1b1c0459-cea4-427a-b6a1-0acce89001e3", "original_filename": "31180.pdf", "stored_filename": "3ed15197-4164-4070-8039-c21b3045ffc9.pdf", "file_path": "app/static/uploads/history/3ed15197-4164-4070-8039-c21b3045ffc9.pdf", "mime_type": "application/pdf", "file_size": 528736, "upload_date": "2025-07-23 15:03:32"}]}, {"id": "72f405ca-2be0-4894-b773-7f4d26b58657", "equipment_id": "59412", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "installed the new UPS, but it didn't work.\r\nA new UPS is installed.\r\nOrder a new UPS.\r\nThe machine is working without UPS.\r\nThe new UPS will be delivered; a biomedical engineer will install it.", "created_at": "2025-07-23 15:09:24", "updated_at": "2025-07-23 15:09:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "79550bdb-ece5-4415-8600-ecf1ed039ac1", "note_id": "72f405ca-2be0-4894-b773-7f4d26b58657", "original_filename": "59412_B.pdf", "stored_filename": "d580b687-ae47-42e8-9141-5026223a5baa.pdf", "file_path": "app/static/uploads/history/d580b687-ae47-42e8-9141-5026223a5baa.pdf", "mime_type": "application/pdf", "file_size": 927553, "upload_date": "2025-07-23 15:09:24"}]}, {"id": "60182077-dea8-4e93-93a5-02d1fc56fb2e", "equipment_id": "59412", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the D400 & MCB2 board.\r\nreplaced the defective 80A fuse 3.1\r\nTune-up & checkup done successfully.", "created_at": "2025-07-23 15:16:30", "updated_at": "2025-07-23 15:16:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bf39e99b-685e-415f-a011-846b3bfb15f0", "note_id": "60182077-dea8-4e93-93a5-02d1fc56fb2e", "original_filename": "59412_C.pdf", "stored_filename": "8f9eecc6-5fe4-48d5-99f6-9c19cacefc52.pdf", "file_path": "app/static/uploads/history/8f9eecc6-5fe4-48d5-99f6-9c19cacefc52.pdf", "mime_type": "application/pdf", "file_size": 516096, "upload_date": "2025-07-23 15:16:30"}]}, {"id": "4363116f-2f73-41ff-80f2-1cf79d2b6a05", "equipment_id": "59412", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "After troubleshooting, found that the D400 main control board is suspected to be defective.\r\nwaiting for quotation.", "created_at": "2025-07-23 15:24:54", "updated_at": "2025-07-23 15:24:55", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d0aab9fb-7eeb-480c-aab5-bbea0d5afcc8", "note_id": "4363116f-2f73-41ff-80f2-1cf79d2b6a05", "original_filename": "59412_D.pdf", "stored_filename": "16d3ecf6-a8db-4f89-9c98-7826f67ab74d.pdf", "file_path": "app/static/uploads/history/16d3ecf6-a8db-4f89-9c98-7826f67ab74d.pdf", "mime_type": "application/pdf", "file_size": 470736, "upload_date": "2025-07-23 15:24:55"}]}, {"id": "dc2060fa-373f-4e73-82c6-3e7b49809c88", "equipment_id": "EQ16371929EX18", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue Description:\r\nThe Hydrofacial machine requires maintenance due to damaged tubing and a slightly crumpled plastic bottle. \r\nThe unit has been sent to the Biomedical Department for evaluation and repair. \r\nAwaiting further assessment and service action.", "created_at": "2025-07-23 15:36:31", "updated_at": "2025-07-23 15:36:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ebf8b140-5da6-4990-a652-6de238ed25d8", "note_id": "dc2060fa-373f-4e73-82c6-3e7b49809c88", "original_filename": "LOG__1006.pdf", "stored_filename": "e621ffed-b500-4aa6-9c26-862f038b03c8.pdf", "file_path": "app/static/uploads/history/e621ffed-b500-4aa6-9c26-862f038b03c8.pdf", "mime_type": "application/pdf", "file_size": 945534, "upload_date": "2025-07-23 15:36:31"}]}, {"id": "3bdc6bd3-103d-40bd-a873-ffb7343d74fc", "equipment_id": "59412", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "2-piece P68 gantry fans replaced with a new one.\r\nThe gantry filter (10856949) and detector filter (10186056) were replaced.\r\nThe detector fan (07071921) and signal brush (10415047) were replaced.\r\nGantry and table greasing done.\r\nQuality constancy and calibrations are done.\r\nsystem is tested and given in working condition.", "created_at": "2025-07-23 15:38:31", "updated_at": "2025-07-23 15:38:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "30fd7149-18e0-4356-ac05-7a5e8cc88a3d", "note_id": "3bdc6bd3-103d-40bd-a873-ffb7343d74fc", "original_filename": "59412_E.pdf", "stored_filename": "62666b9e-d262-4283-9921-a85bc2d41003.pdf", "file_path": "app/static/uploads/history/62666b9e-d262-4283-9921-a85bc2d41003.pdf", "mime_type": "application/pdf", "file_size": 598672, "upload_date": "2025-07-23 15:38:31"}]}, {"id": "1a5c2c64-d461-45ea-a1da-902e085a28d0", "equipment_id": "360713-M1600662004", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue Description:\r\nECG lead disconnection reported.\r\nAssessed and resolved by the Biomedical Department.", "created_at": "2025-07-23 15:39:39", "updated_at": "2025-07-23 15:39:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "944084e2-bdd4-425d-b47c-dbe05a482fd1", "note_id": "1a5c2c64-d461-45ea-a1da-902e085a28d0", "original_filename": "LOG__2327.pdf", "stored_filename": "5d60e1e7-30de-4e74-963f-3afab678af8d.pdf", "file_path": "app/static/uploads/history/5d60e1e7-30de-4e74-963f-3afab678af8d.pdf", "mime_type": "application/pdf", "file_size": 1005231, "upload_date": "2025-07-23 15:39:39"}]}, {"id": "7c227423-1f2d-4b60-895a-971d1775a924", "equipment_id": "ME3005596", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Issue Description:\r\nThe ERO-SCAN machine probe was found detached. \r\nAlthough reattached, it remained non-functional. \r\nA spare probe was provided by the Biomedical Department and the unit is now operational.", "created_at": "2025-07-23 15:43:34", "updated_at": "2025-07-23 15:43:34", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f112c4ad-8706-4bc4-9f29-ac17b94568f1", "note_id": "7c227423-1f2d-4b60-895a-971d1775a924", "original_filename": "LOG__1954.pdf", "stored_filename": "8a7176d9-bcab-4583-80c0-f15e1cb59956.pdf", "file_path": "app/static/uploads/history/8a7176d9-bcab-4583-80c0-f15e1cb59956.pdf", "mime_type": "application/pdf", "file_size": 1034607, "upload_date": "2025-07-23 15:43:34"}]}, {"id": "06d4e82c-1d82-4ace-929b-745f12ee1c49", "equipment_id": "05AF000643", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue Description:\r\nThe SPO2 machine probe was not functioning. \r\nThe issue was reported to the Biomedical Department, which provided a spare probe. \r\nThe machine is now operational.", "created_at": "2025-07-23 15:46:21", "updated_at": "2025-07-23 15:46:21", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fd65f3c9-8b8b-46f7-9443-4d88b09bda7f", "note_id": "06d4e82c-1d82-4ace-929b-745f12ee1c49", "original_filename": "05AF000643-_b.pdf", "stored_filename": "a76c159c-418b-46ad-8aef-8fd4694243e2.pdf", "file_path": "app/static/uploads/history/a76c159c-418b-46ad-8aef-8fd4694243e2.pdf", "mime_type": "application/pdf", "file_size": 1022815, "upload_date": "2025-07-23 15:46:21"}]}, {"id": "aaa70e4b-b461-4c4a-acfb-b37600ca60d4", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance is done.\r\nTest tools and general QA were performed.\r\nSyringe backup was done.\r\ntested and handed over the system for patients scans.", "created_at": "2025-07-23 15:47:27", "updated_at": "2025-07-23 15:47:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a8a8fde4-8702-426f-9284-23977c3ff1b6", "note_id": "aaa70e4b-b461-4c4a-acfb-b37600ca60d4", "original_filename": "31180_B.pdf", "stored_filename": "40cc86c4-50c6-4fb2-b993-0461c9f8e1d1.pdf", "file_path": "app/static/uploads/history/40cc86c4-50c6-4fb2-b993-0461c9f8e1d1.pdf", "mime_type": "application/pdf", "file_size": 503376, "upload_date": "2025-07-23 15:47:27"}]}, {"id": "041fe227-79a4-4a74-b860-be600801d52a", "equipment_id": "SOIBM3HFD00023R", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Issue Description:\r\nThe ultrasound machine in the OB clinic displayed a blank screen. \r\nThe Biomedical Department assessed the unit and performed a software update. \r\nThe machine is now functioning properly.", "created_at": "2025-07-23 15:49:28", "updated_at": "2025-07-23 15:49:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "901e0cec-7c1f-4f84-b550-2849dca624c7", "note_id": "041fe227-79a4-4a74-b860-be600801d52a", "original_filename": "LOG__1865.pdf", "stored_filename": "2c84226d-b936-4666-aefa-a71df5578400.pdf", "file_path": "app/static/uploads/history/2c84226d-b936-4666-aefa-a71df5578400.pdf", "mime_type": "application/pdf", "file_size": 1078175, "upload_date": "2025-07-23 15:49:28"}]}, {"id": "987b7f1c-712a-4535-93c2-28cd819f7d60", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance is done.\r\nMagnet and cooling status checked.\r\nSystem backup taken.\r\nQuality assurance is done.\r\nsystem is tested and given working condition.", "created_at": "2025-07-23 15:50:39", "updated_at": "2025-07-23 15:50:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bf0765f3-0e17-4bbe-a33a-4c87028775f1", "note_id": "987b7f1c-712a-4535-93c2-28cd819f7d60", "original_filename": "31180_C.pdf", "stored_filename": "306931a4-3941-439e-a5d3-ecf0e35c060b.pdf", "file_path": "app/static/uploads/history/306931a4-3941-439e-a5d3-ecf0e35c060b.pdf", "mime_type": "application/pdf", "file_size": 519904, "upload_date": "2025-07-23 15:50:39"}]}, {"id": "73f81455-2ec1-45b0-92b1-84a875d37e20", "equipment_id": "551101303630M9200121", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "inspected unit operation with backup.\r\nThe unit is working normally.", "created_at": "2025-07-23 16:02:30", "updated_at": "2025-07-23 16:02:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7ad24b97-afbb-40fb-8758-55664ea1fe4d", "note_id": "73f81455-2ec1-45b0-92b1-84a875d37e20", "original_filename": "551101303630M9200121.pdf", "stored_filename": "f86b33ed-b2ae-4177-86c3-1f4a643b9c02.pdf", "file_path": "app/static/uploads/history/f86b33ed-b2ae-4177-86c3-1f4a643b9c02.pdf", "mime_type": "application/pdf", "file_size": 488848, "upload_date": "2025-07-23 16:02:30"}]}, {"id": "77578c5b-f906-4ec6-85d6-beb3ce1ef294", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Compression was not releasing.\r\nchanged setting and testing.\r\nunit is working okay.", "created_at": "2025-07-23 16:04:41", "updated_at": "2025-07-23 16:04:41", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b8e914fa-780e-4679-be9d-2faf368f85d6", "note_id": "77578c5b-f906-4ec6-85d6-beb3ce1ef294", "original_filename": "HELC0063C1.pdf", "stored_filename": "fddd95e1-8b62-47dc-b23a-8e332c56a4a4.pdf", "file_path": "app/static/uploads/history/fddd95e1-8b62-47dc-b23a-8e332c56a4a4.pdf", "mime_type": "application/pdf", "file_size": 501952, "upload_date": "2025-07-23 16:04:41"}]}, {"id": "4ba092a7-b7d0-427e-ac0d-a52794c1b4e8", "equipment_id": "4S2404100032", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Issue Description:\r\nWheelchair #19 was found to have a missing screw. \r\nThe unit was sent to the Biomedical Department and repaired by the Maintenance team.\r\n The wheelchair is now fully functional.", "created_at": "2025-07-23 16:08:37", "updated_at": "2025-07-23 16:08:37", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fb9eb629-4a0b-4347-ae85-231a7fe811fe", "note_id": "4ba092a7-b7d0-427e-ac0d-a52794c1b4e8", "original_filename": "4S2404100032.pdf", "stored_filename": "bb34c85e-d4a3-485c-a67b-c01bb50f755b.pdf", "file_path": "app/static/uploads/history/bb34c85e-d4a3-485c-a67b-c01bb50f755b.pdf", "mime_type": "application/pdf", "file_size": 991374, "upload_date": "2025-07-23 16:08:37"}]}, {"id": "21254451-c04c-4535-8c7c-f6fa12ff65f2", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced the parts with new ones.\r\nperformed daily quality assurance tests and passed.\r\ndone phantom tests.\r\nchecked all the program settings.\r\nchecked and found the unit is working in good condition.", "created_at": "2025-07-23 16:09:25", "updated_at": "2025-07-23 16:09:25", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e3161a0b-216f-4fa6-ab51-7bdeb74cdeba", "note_id": "21254451-c04c-4535-8c7c-f6fa12ff65f2", "original_filename": "HELC0063C1_B.pdf", "stored_filename": "0c4a18aa-7e7c-44a2-9b76-5f7f802042eb.pdf", "file_path": "app/static/uploads/history/0c4a18aa-7e7c-44a2-9b76-5f7f802042eb.pdf", "mime_type": "application/pdf", "file_size": 597408, "upload_date": "2025-07-23 16:09:25"}]}, {"id": "13b36447-8f86-4b73-83f7-27f46c298cfd", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "after being investigated by the manufacturer.\r\nrecommended to change the spare parts of the compression board and controller unit.\r\nwaiting for the quotation for replacement.", "created_at": "2025-07-23 16:16:00", "updated_at": "2025-07-23 16:16:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "44ff3680-3ce4-494d-aa37-53fac3e1d1e8", "note_id": "13b36447-8f86-4b73-83f7-27f46c298cfd", "original_filename": "HELC0063C1_C.pdf", "stored_filename": "c627ffce-ba71-4700-91b3-dde18bc3c2f5.pdf", "file_path": "app/static/uploads/history/c627ffce-ba71-4700-91b3-dde18bc3c2f5.pdf", "mime_type": "application/pdf", "file_size": 530000, "upload_date": "2025-07-23 16:16:00"}]}, {"id": "90c8c39d-a7e6-452d-ab2a-7f8f0b6c539a", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "from the manufacturer, check the log files and signal status.\r\nwaiting for the update.", "created_at": "2025-07-23 16:17:44", "updated_at": "2025-07-23 16:17:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3e6a2d77-364f-4a9a-9672-a4d7eeb41a90", "note_id": "90c8c39d-a7e6-452d-ab2a-7f8f0b6c539a", "original_filename": "HELC0063C1_D.pdf", "stored_filename": "f9891f4c-e21e-4924-9c2b-7abf55bd8a43.pdf", "file_path": "app/static/uploads/history/f9891f4c-e21e-4924-9c2b-7abf55bd8a43.pdf", "mime_type": "application/pdf", "file_size": 512880, "upload_date": "2025-07-23 16:17:44"}]}, {"id": "b8d6ba45-068a-4af9-b9b8-967168b5d2cb", "equipment_id": "L11-015D254", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The samarium and detector have been replaced.\r\nThe software has been upgraded.\r\nUnit calibration is performed.\r\nunit found working well.", "created_at": "2025-07-23 16:19:35", "updated_at": "2025-07-23 16:19:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e3ef8d15-2a05-434e-a383-945122a40a06", "note_id": "b8d6ba45-068a-4af9-b9b8-967168b5d2cb", "original_filename": "L11-015D254.pdf", "stored_filename": "67a69ebe-711c-4358-9201-3ef531862a4c.pdf", "file_path": "app/static/uploads/history/67a69ebe-711c-4358-9201-3ef531862a4c.pdf", "mime_type": "application/pdf", "file_size": 341872, "upload_date": "2025-07-23 16:19:35"}]}, {"id": "2a36ba22-e11c-4cb6-a09f-9122c748aadd", "equipment_id": "K5812-7416", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Installed a new X-ray printer.\r\nconfigured printer with press okay.\r\nunit tested okay.", "created_at": "2025-07-23 16:23:15", "updated_at": "2025-07-23 16:23:15", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "67f31d5b-5b4b-4500-b652-1066e3c6b491", "note_id": "2a36ba22-e11c-4cb6-a09f-9122c748aadd", "original_filename": "log_no._2447.pdf", "stored_filename": "8fd85fc0-3e3c-443b-ba04-34f29954d6d9.pdf", "file_path": "app/static/uploads/history/8fd85fc0-3e3c-443b-ba04-34f29954d6d9.pdf", "mime_type": "application/pdf", "file_size": 1078882, "upload_date": "2025-07-23 16:23:15"}]}, {"id": "02f71b47-43ee-407f-82d0-362c1a7d85fa", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance is done.\r\nQA is done.\r\nhanded over in proper working condition.", "created_at": "2025-07-23 16:25:37", "updated_at": "2025-07-23 16:25:37", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "822462e1-40cb-41e7-8faf-31a839092aa0", "note_id": "02f71b47-43ee-407f-82d0-362c1a7d85fa", "original_filename": "31180_D.pdf", "stored_filename": "b8af623a-2c40-4c6b-8eae-45ce4b1523d9.pdf", "file_path": "app/static/uploads/history/b8af623a-2c40-4c6b-8eae-45ce4b1523d9.pdf", "mime_type": "application/pdf", "file_size": 459152, "upload_date": "2025-07-23 16:25:37"}]}, {"id": "bc4cba77-f011-47e9-bf01-e4bd4179be87", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked and found that the compression is not detecting even the compressed condition.\r\nneed technical supplies from the maintenance.", "created_at": "2025-07-23 16:29:04", "updated_at": "2025-07-23 16:29:04", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "603e9ce3-86e7-4991-84f9-60038cb871ce", "note_id": "bc4cba77-f011-47e9-bf01-e4bd4179be87", "original_filename": "HELC0063C1_E.pdf", "stored_filename": "f2246d25-b432-4d4d-aa9e-b6dabf76d99f.pdf", "file_path": "app/static/uploads/history/f2246d25-b432-4d4d-aa9e-b6dabf76d99f.pdf", "mime_type": "application/pdf", "file_size": 549248, "upload_date": "2025-07-23 16:29:04"}]}, {"id": "47b83f91-fc74-41ef-bf63-44994416a6d9", "equipment_id": "14A110301", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "replaced the touch screen CPU board.\r\nchecked and restricted system with online support.\r\nIn general, the operation is okay.\r\nNote: the unit is old, software and hardware support is limited, and failure support cannot be guaranteed.", "created_at": "2025-07-23 16:55:31", "updated_at": "2025-07-23 16:55:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2e0ab2b8-694d-4663-a49b-588c8d5e7dae", "note_id": "47b83f91-fc74-41ef-bf63-44994416a6d9", "original_filename": "log_no._1783.pdf", "stored_filename": "82145b00-6497-4c3e-983c-f9cf7102904a.pdf", "file_path": "app/static/uploads/history/82145b00-6497-4c3e-983c-f9cf7102904a.pdf", "mime_type": "application/pdf", "file_size": 553392, "upload_date": "2025-07-23 16:55:31"}]}, {"id": "7e417b1b-4732-4c26-abf1-e864f013bd51", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the system.\r\nenabled compressor\r\nrelease activation.\r\nchecked all operations are okay.", "created_at": "2025-07-23 16:59:20", "updated_at": "2025-07-23 16:59:20", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f2a84069-f6a4-4816-ab2e-b7cec2f9c655", "note_id": "7e417b1b-4732-4c26-abf1-e864f013bd51", "original_filename": "HELC0063C1_F.pdf", "stored_filename": "8372ddb7-a32b-4c6a-8433-b277c139885a.pdf", "file_path": "app/static/uploads/history/8372ddb7-a32b-4c6a-8433-b277c139885a.pdf", "mime_type": "application/pdf", "file_size": 443104, "upload_date": "2025-07-23 16:59:20"}]}, {"id": "fde075e6-3a6f-484f-8e83-806b2bf4ab61", "equipment_id": "HELC-0063-C1", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "checked the unit and found that the keyboard and mouse for the PC system were not communicating.\r\nReset the power for the USB receiver hub.\r\nchecked and found that the unit is working properly.", "created_at": "2025-07-23 17:08:02", "updated_at": "2025-07-23 17:08:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2330082b-bf30-4e7c-a0f8-5b5174954470", "note_id": "fde075e6-3a6f-484f-8e83-806b2bf4ab61", "original_filename": "HELC0063C1_G.pdf", "stored_filename": "bc884b8c-2ce6-4d8c-ac00-49eb49e12a8f.pdf", "file_path": "app/static/uploads/history/bc884b8c-2ce6-4d8c-ac00-49eb49e12a8f.pdf", "mime_type": "application/pdf", "file_size": 476000, "upload_date": "2025-07-23 17:08:02"}]}, {"id": "922b40f0-fedc-4b34-9e8d-9872bbc294fa", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance was done.\r\nTest tools for RF and gradient were done.\r\nResults were found to be in specification.\r\nThe backup was done.\r\ntested and handed over for patient scans.", "created_at": "2025-07-23 17:17:42", "updated_at": "2025-07-23 17:17:42", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4352a4e3-c28f-40fc-b284-7e5389d4ffe4", "note_id": "922b40f0-fedc-4b34-9e8d-9872bbc294fa", "original_filename": "31180_E.pdf", "stored_filename": "eb65a3cd-62d8-4f18-812f-02bec943c2b5.pdf", "file_path": "app/static/uploads/history/eb65a3cd-62d8-4f18-812f-02bec943c2b5.pdf", "mime_type": "application/pdf", "file_size": 473152, "upload_date": "2025-07-23 17:17:42"}]}, {"id": "350876f4-6e69-447b-9d8a-7de4c1dd063e", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Preventive maintenance was done.\r\nThe backup was done.\r\ntested and handed over the patient scan.", "created_at": "2025-07-23 17:19:06", "updated_at": "2025-07-23 17:19:06", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1ec92c03-ea11-4846-b6ea-abf75d1e8ac0", "note_id": "350876f4-6e69-447b-9d8a-7de4c1dd063e", "original_filename": "31180_F.pdf", "stored_filename": "42b1775e-da2e-4dc1-9dc5-b02ba50a761b.pdf", "file_path": "app/static/uploads/history/42b1775e-da2e-4dc1-9dc5-b02ba50a761b.pdf", "mime_type": "application/pdf", "file_size": 452896, "upload_date": "2025-07-23 17:19:06"}]}, {"id": "3533f916-b7a5-4a3c-b7f0-ffff06b1b502", "equipment_id": "L11-015D254", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "semarium has been checked.\r\nPhysical inspection and aging test done.\r\nA log file has been taken for analysing.\r\nSend it to manufacturer and waiting for the update.", "created_at": "2025-07-23 17:24:29", "updated_at": "2025-07-23 17:24:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "396e9e1a-d761-4518-b570-52d1139c81eb", "note_id": "3533f916-b7a5-4a3c-b7f0-ffff06b1b502", "original_filename": "log_no._1781.pdf", "stored_filename": "a00dcaf7-91f0-46ca-a770-74ff1f895ce5.pdf", "file_path": "app/static/uploads/history/a00dcaf7-91f0-46ca-a770-74ff1f895ce5.pdf", "mime_type": "application/pdf", "file_size": 355600, "upload_date": "2025-07-23 17:24:29"}]}, {"id": "5b6da167-2aa0-429f-a9a5-8c16f99457c4", "equipment_id": "11J-1798", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Observed that the temperature display was not functioning. \r\nReplaced the display battery with a new one. \r\nVerified operation after replacement and confirmed that the unit is now functioning properly.", "created_at": "2025-07-24 06:31:24", "updated_at": "2025-07-24 06:31:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f9b22b18-619a-4e88-a471-8845936ef560", "note_id": "5b6da167-2aa0-429f-a9a5-8c16f99457c4", "original_filename": "11J_1798.pdf", "stored_filename": "fdba41b7-2076-47ac-96c8-216e5a6c31b6.pdf", "file_path": "app/static/uploads/history/fdba41b7-2076-47ac-96c8-216e5a6c31b6.pdf", "mime_type": "application/pdf", "file_size": 507728, "upload_date": "2025-07-24 06:31:24"}]}, {"id": "c7453bbf-bef5-497e-805f-0f66b98c4f9a", "equipment_id": "11J-1798", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the battery. \r\nVerified that the unit is now working properly. \r\nChecked all connections and tested.", "created_at": "2025-07-24 06:46:12", "updated_at": "2025-07-24 06:46:12", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1f8228b6-6335-419b-a29e-861579952996", "note_id": "c7453bbf-bef5-497e-805f-0f66b98c4f9a", "original_filename": "11J_1798-_b.pdf", "stored_filename": "456af009-c616-446a-96b3-a1f55785564b.pdf", "file_path": "app/static/uploads/history/456af009-c616-446a-96b3-a1f55785564b.pdf", "mime_type": "application/pdf", "file_size": 494960, "upload_date": "2025-07-24 06:46:12"}]}, {"id": "42ab4d5d-6461-4ba6-a364-d383f3969a4a", "equipment_id": "260597-M19711210001", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected the NIBP function in the XMI module. \r\nObserved that the NIBP pressure was not increasing and was getting blocked midway. Swapped with another module and confirmed that the NIBP function works properly with the replacement. \r\nThis indicates the XMI module is faulty. \r\nWe will follow up with the manufacturer and provide an update ASAP.", "created_at": "2025-07-24 06:54:21", "updated_at": "2025-07-24 06:54:21", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5a2167b3-0e8a-4bee-809a-1c49f4c12835", "note_id": "42ab4d5d-6461-4ba6-a364-d383f3969a4a", "original_filename": "M19711210001-_b.pdf", "stored_filename": "3e389d8c-c7b0-475f-b05e-435ed8664ee3.pdf", "file_path": "app/static/uploads/history/3e389d8c-c7b0-475f-b05e-435ed8664ee3.pdf", "mime_type": "application/pdf", "file_size": 548768, "upload_date": "2025-07-24 06:54:21"}]}, {"id": "81fb44d9-b6a4-49a7-9780-7f8d79770b0a", "equipment_id": "2008-20022414", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Tested the unit using an analyzer and confirmed it is functioning correctly. \r\nVerified the backup battery, which is also working properly. \r\nPerformed additional tests and found the unit to be operating properly.", "created_at": "2025-07-24 07:00:06", "updated_at": "2025-07-24 07:00:06", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "498ae41f-b1d6-4a50-a7ab-d0c15419406c", "note_id": "81fb44d9-b6a4-49a7-9780-7f8d79770b0a", "original_filename": "200820022414.pdf", "stored_filename": "a4201a30-4020-4147-9ae2-1ba9a0531cc9.pdf", "file_path": "app/static/uploads/history/a4201a30-4020-4147-9ae2-1ba9a0531cc9.pdf", "mime_type": "application/pdf", "file_size": 583776, "upload_date": "2025-07-24 07:00:06"}]}, {"id": "1a8ce88e-87eb-4f23-88ad-895fbfc00972", "equipment_id": "11J-1798", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the display battery with a new one. \r\nChecked and confirmed that the unit is functioning properly.", "created_at": "2025-07-24 07:06:24", "updated_at": "2025-07-24 07:06:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "adfa1fc2-4c2c-44f2-80fd-76dfa7baf458", "note_id": "1a8ce88e-87eb-4f23-88ad-895fbfc00972", "original_filename": "11J_1798-_c.pdf", "stored_filename": "5bf736c9-7b69-46f4-be92-ab50753c2ab3.pdf", "file_path": "app/static/uploads/history/5bf736c9-7b69-46f4-be92-ab50753c2ab3.pdf", "mime_type": "application/pdf", "file_size": 494256, "upload_date": "2025-07-24 07:06:24"}]}, {"id": "b0e0728d-9ef3-424b-ae5d-c43e7432a187", "equipment_id": "860013825", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The NaCl piston side was not functioning. \r\nReplacing the NaCl motor resolved the issue. \r\nAdditionally, the housing cover was replaced to prevent liquid from dripping into the unit.", "created_at": "2025-07-24 07:26:33", "updated_at": "2025-07-24 07:26:33", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2d93475a-dfc2-4e32-bb10-d36368855db6", "note_id": "b0e0728d-9ef3-424b-ae5d-c43e7432a187", "original_filename": "860013825.pdf", "stored_filename": "83dea642-ba02-4eeb-a7f7-322163d04aae.pdf", "file_path": "app/static/uploads/history/83dea642-ba02-4eeb-a7f7-322163d04aae.pdf", "mime_type": "application/pdf", "file_size": 536336, "upload_date": "2025-07-24 07:26:33"}]}, {"id": "61cd92d0-90c4-47ba-bbf4-bedaa3731375", "equipment_id": "*************", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Checked the defibrillator shock function using an analyzer and confirmed it is working correctly. \r\nFound that the battery backup is not functioning and requires replacement. \r\nPerformed additional tests and confirmed the unit is operating properly.", "created_at": "2025-07-24 07:37:28", "updated_at": "2025-07-24 07:37:28", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4397eb95-d435-4528-982c-ed82a817ad2b", "note_id": "61cd92d0-90c4-47ba-bbf4-bedaa3731375", "original_filename": "*************_b.pdf", "stored_filename": "0d040d22-52c4-4c7a-91f7-1ebf241fd693.pdf", "file_path": "app/static/uploads/history/0d040d22-52c4-4c7a-91f7-1ebf241fd693.pdf", "mime_type": "application/pdf", "file_size": 588256, "upload_date": "2025-07-24 07:37:28"}]}, {"id": "8322ca06-da41-4d97-a376-af13ff4abdb9", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "KTT chiller not functioning. It has been removed and sent to the workshop for repair.", "created_at": "2025-07-24 07:43:36", "updated_at": "2025-07-24 07:43:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "407b8653-f52c-45b3-9047-157abb26b4eb", "note_id": "8322ca06-da41-4d97-a376-af13ff4abdb9", "original_filename": "31180-_G.pdf", "stored_filename": "3efb711f-9525-4f63-8205-4119cfa7926d.pdf", "file_path": "app/static/uploads/history/3efb711f-9525-4f63-8205-4119cfa7926d.pdf", "mime_type": "application/pdf", "file_size": 1013059, "upload_date": "2025-07-24 07:43:36"}]}, {"id": "a5c29fec-6ba1-4fe0-b70a-1cc4f0fe615a", "equipment_id": "T7296", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found the internal plate displaced. \r\nRepositioned and secured the plate properly. \r\nChecked and confirmed that the unit is now functioning correctly.", "created_at": "2025-07-24 07:50:49", "updated_at": "2025-07-24 07:50:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9848c856-eebc-4887-b8d6-196a7cc86f31", "note_id": "a5c29fec-6ba1-4fe0-b70a-1cc4f0fe615a", "original_filename": "T7296-_b.pdf", "stored_filename": "8df50dcb-b43e-4667-a24f-cca5d5fd4cbc.pdf", "file_path": "app/static/uploads/history/8df50dcb-b43e-4667-a24f-cca5d5fd4cbc.pdf", "mime_type": "application/pdf", "file_size": 475984, "upload_date": "2025-07-24 07:50:49"}]}, {"id": "1602b01c-9f4b-45e4-9c58-5dc47c4c27ba", "equipment_id": "14A110301", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit problem caused by power failure.", "created_at": "2025-07-24 08:50:24", "updated_at": "2025-07-24 08:50:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "43f6eba9-3991-4b96-88f1-0e3bb8bd46a4", "note_id": "1602b01c-9f4b-45e4-9c58-5dc47c4c27ba", "original_filename": "1783-_b.pdf", "stored_filename": "fa2c472b-578d-4e1b-843a-75c9d99d46a7.pdf", "file_path": "app/static/uploads/history/fa2c472b-578d-4e1b-843a-75c9d99d46a7.pdf", "mime_type": "application/pdf", "file_size": 896494, "upload_date": "2025-07-24 08:50:24"}]}, {"id": "52a6b4b1-d89a-4c59-9b91-e0de983621cf", "equipment_id": "31180", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "CM side was not detecting the 200 ml syringe. \r\nFound the sensor covered with contrast media. \r\nCleaned the sensor, which resolved the issue. \r\nVerified that the unit is now working properly.", "created_at": "2025-07-24 08:55:58", "updated_at": "2025-07-24 08:55:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2b24c9cd-4423-476a-9099-b23bfb13d3d3", "note_id": "52a6b4b1-d89a-4c59-9b91-e0de983621cf", "original_filename": "31180-_H.pdf", "stored_filename": "ad46ba16-5fbb-40ce-b233-cf8d6a0f3660.pdf", "file_path": "app/static/uploads/history/ad46ba16-5fbb-40ce-b233-cf8d6a0f3660.pdf", "mime_type": "application/pdf", "file_size": 1444447, "upload_date": "2025-07-24 08:55:58"}]}, {"id": "99675225-8e36-4c13-ac3b-3c3fd49ea698", "equipment_id": "620030013", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The unit is currently functioning properly. \r\nHowever, the inlet of the valve was found to be broken, which may cause issues in the future.", "created_at": "2025-07-24 08:59:36", "updated_at": "2025-07-24 08:59:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "31e61ca3-0d1f-4b1e-98e2-eae50d30d2e7", "note_id": "99675225-8e36-4c13-ac3b-3c3fd49ea698", "original_filename": "620030013.pdf", "stored_filename": "9ccfa375-4f4e-4ee2-856d-dad09096527b.pdf", "file_path": "app/static/uploads/history/9ccfa375-4f4e-4ee2-856d-dad09096527b.pdf", "mime_type": "application/pdf", "file_size": 374016, "upload_date": "2025-07-24 08:59:36"}]}, {"id": "5232658c-4161-4e50-b61e-fe781ed7501f", "equipment_id": "14A110301", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Faulty detector due to power failure. \r\nReset and rechecked, unit is working properly.", "created_at": "2025-07-24 09:19:44", "updated_at": "2025-07-24 09:19:44", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "088adad1-9fc1-4e43-817f-c677b73cc35b", "note_id": "5232658c-4161-4e50-b61e-fe781ed7501f", "original_filename": "1783-_c.pdf", "stored_filename": "61c2f9fc-7a57-473d-ad59-3fbe9cb7d2ee.pdf", "file_path": "app/static/uploads/history/61c2f9fc-7a57-473d-ad59-3fbe9cb7d2ee.pdf", "mime_type": "application/pdf", "file_size": 556096, "upload_date": "2025-07-24 09:19:44"}]}, {"id": "d026c641-fa1c-481c-b493-6ed0eef23ffe", "equipment_id": "14A110301", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "App box failure due to power failure. \r\nReset the unit and found to be working properly.", "created_at": "2025-07-24 09:24:20", "updated_at": "2025-07-24 09:24:20", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fc0bd9de-c12a-4986-af43-9c9d0d5b218c", "note_id": "d026c641-fa1c-481c-b493-6ed0eef23ffe", "original_filename": "1783-_d.pdf", "stored_filename": "4682d1aa-d870-4bc0-a187-d8590cc711da.pdf", "file_path": "app/static/uploads/history/4682d1aa-d870-4bc0-a187-d8590cc711da.pdf", "mime_type": "application/pdf", "file_size": 557296, "upload_date": "2025-07-24 09:24:20"}]}, {"id": "e2438208-8d63-42ea-8dd0-d9ecddb200b2", "equipment_id": "14A110301", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "APP Box Error.\r\nReset process done.\r\nUnit is working properly.", "created_at": "2025-07-24 09:26:35", "updated_at": "2025-07-24 09:26:35", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "e3ec5c87-4d21-4f9e-b4ae-ca408cf4ea32", "note_id": "e2438208-8d63-42ea-8dd0-d9ecddb200b2", "original_filename": "1783-_e.pdf", "stored_filename": "e7624f3a-23a2-41d0-8cf9-adbd131c27b1.pdf", "file_path": "app/static/uploads/history/e7624f3a-23a2-41d0-8cf9-adbd131c27b1.pdf", "mime_type": "application/pdf", "file_size": 548928, "upload_date": "2025-07-24 09:26:35"}]}, {"id": "ef84f540-e7cb-42ab-8a3e-84d59a0810cf", "equipment_id": "551101303630M9200121", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced the battery pack.\r\nVerified that the unit is now working properly.", "created_at": "2025-07-24 09:29:11", "updated_at": "2025-07-24 09:29:11", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "dfc851fe-755d-4adb-a6ef-4a72ec0bd1b7", "note_id": "ef84f540-e7cb-42ab-8a3e-84d59a0810cf", "original_filename": "LOG__1787.pdf", "stored_filename": "15524552-5737-4e72-95b3-8def6c91eaac.pdf", "file_path": "app/static/uploads/history/15524552-5737-4e72-95b3-8def6c91eaac.pdf", "mime_type": "application/pdf", "file_size": 496320, "upload_date": "2025-07-24 09:29:11"}]}, {"id": "ec8afd4b-654b-4c93-9a45-6f39962b26f6", "equipment_id": "226366", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Dental chair handle is not functioning. Additionally, suction modes are reversed — low suction mode produces high suction, and high suction mode produces low suction. \r\nBiomedical assessment was completed, and the issue has been fixed.", "created_at": "2025-07-24 09:51:41", "updated_at": "2025-07-24 09:51:41", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b0f7aa43-f129-45b4-822f-ffe0e4d5cd31", "note_id": "ec8afd4b-654b-4c93-9a45-6f39962b26f6", "original_filename": "226366.pdf", "stored_filename": "e69ebc48-2ffe-43e0-a972-58df1dd56c1f.pdf", "file_path": "app/static/uploads/history/e69ebc48-2ffe-43e0-a972-58df1dd56c1f.pdf", "mime_type": "application/pdf", "file_size": 1106303, "upload_date": "2025-07-24 09:51:41"}]}, {"id": "482df019-f96b-422c-a24f-1db3ef85d421", "equipment_id": "16-00425", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Fotona laser unit suddenly ceased emitting a laser beam during operation. \r\nIssue reported to the Biomedical Department. \r\nAwaiting assessment and repair.", "created_at": "2025-07-24 14:19:05", "updated_at": "2025-07-24 14:19:05", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "0e072d4a-535c-427a-952b-152a205627e3", "note_id": "482df019-f96b-422c-a24f-1db3ef85d421", "original_filename": "16100425-_h.pdf", "stored_filename": "c7c935b2-7378-4713-ac06-6b38f6e4ef03.pdf", "file_path": "app/static/uploads/history/c7c935b2-7378-4713-ac06-6b38f6e4ef03.pdf", "mime_type": "application/pdf", "file_size": 1153759, "upload_date": "2025-07-24 14:19:05"}]}, {"id": "1935874f-151b-4914-b518-7898e977bf9a", "equipment_id": "168639", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Nebulizer with damaged connector tube due to misuse. \r\nUnit has been sent to the Biomedical Department for evaluation. \r\nStaff involved received a salary deduction as a penalty. \r\nThe unit is currently in Biomed and has been declared obsolete.", "created_at": "2025-07-24 14:43:24", "updated_at": "2025-07-24 14:43:24", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "92956218-053d-4852-87bc-50bb5179d3fa", "note_id": "1935874f-151b-4914-b518-7898e977bf9a", "original_filename": "168639.pdf", "stored_filename": "05a3c307-2e1d-4db9-b21c-093648fd7bec.pdf", "file_path": "app/static/uploads/history/05a3c307-2e1d-4db9-b21c-093648fd7bec.pdf", "mime_type": "application/pdf", "file_size": 1049951, "upload_date": "2025-07-24 14:43:24"}]}, {"id": "50246691-7021-4309-8a3e-aa55a032ccf7", "equipment_id": "17030021", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Charger was found broken.\r\nBiomedical provided batteries.\r\nUnit is now fully functioning.", "created_at": "2025-07-24 14:57:03", "updated_at": "2025-07-24 14:57:03", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "eea16b27-0ed8-46a8-9751-5f4a2abc90e2", "note_id": "50246691-7021-4309-8a3e-aa55a032ccf7", "original_filename": "LOG__1619.pdf", "stored_filename": "60b79984-f706-43c2-811f-3db6c88e0720.pdf", "file_path": "app/static/uploads/history/60b79984-f706-43c2-811f-3db6c88e0720.pdf", "mime_type": "application/pdf", "file_size": 2182847, "upload_date": "2025-07-24 14:57:03"}]}, {"id": "590745c0-b2be-47b5-974f-1fd597b52fa9", "equipment_id": "80232658", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit was not charging. \r\nCord was checked and shortened.\r\nIssue resolved, unit now working properly.", "created_at": "2025-07-24 15:02:15", "updated_at": "2025-07-24 15:02:15", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8cd79e29-0beb-42b9-88e5-3db5f1914155", "note_id": "590745c0-b2be-47b5-974f-1fd597b52fa9", "original_filename": "80232658.pdf", "stored_filename": "1cdffb08-3442-441e-b0b1-da8d3b03a77f.pdf", "file_path": "app/static/uploads/history/1cdffb08-3442-441e-b0b1-da8d3b03a77f.pdf", "mime_type": "application/pdf", "file_size": 1089007, "upload_date": "2025-07-24 15:02:15"}]}, {"id": "7be2e5ac-1412-4205-9f10-50d42a2ed2ad", "equipment_id": "SOIBM3HDC00045A", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: OB ultrasound machine was non-functional. Assessed by Biomedical Department. Software upgrade performed. Unit functionality confirmed post-upgrade", "created_at": "2025-07-24 15:08:05", "updated_at": "2025-07-24 15:08:05", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "b89748d2-599c-4cee-a6d7-9bb405726a0a", "note_id": "7be2e5ac-1412-4205-9f10-50d42a2ed2ad", "original_filename": "LOG__1878.pdf", "stored_filename": "59aa366b-87a6-4033-b0cd-44995a39ad9e.pdf", "file_path": "app/static/uploads/history/59aa366b-87a6-4033-b0cd-44995a39ad9e.pdf", "mime_type": "application/pdf", "file_size": 1091391, "upload_date": "2025-07-24 15:08:05"}]}, {"id": "9edba790-e570-4cac-bcb8-2264bb0f2d2f", "equipment_id": "SOIBM3HDC00045A", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Screen suddenly went black. \r\nA backup machine was provided for temporary use. \r\nAfter a software upgrade, the unit was tested, found to be functioning properly, and returned to the clinic.", "created_at": "2025-07-24 15:12:45", "updated_at": "2025-07-24 15:12:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "43ad968d-1cc4-4810-8fd2-ff44b5ca0601", "note_id": "9edba790-e570-4cac-bcb8-2264bb0f2d2f", "original_filename": "LOG__1878-_b.pdf", "stored_filename": "9e44434f-2bb0-4971-837b-c058da254954.pdf", "file_path": "app/static/uploads/history/9e44434f-2bb0-4971-837b-c058da254954.pdf", "mime_type": "application/pdf", "file_size": 1055391, "upload_date": "2025-07-24 15:12:45"}]}, {"id": "002256b0-c063-4fe7-832b-f1e7995c9a35", "equipment_id": "9914-9035-5009", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Water leakage observed from the machine. \r\nUnit sent to the Biomedical Department. \r\nCurrently awaiting repair.", "created_at": "2025-07-24 15:21:36", "updated_at": "2025-07-24 15:21:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d4b435ea-8831-4e6b-bbe0-caca1f7f8f20", "note_id": "002256b0-c063-4fe7-832b-f1e7995c9a35", "original_filename": "9914-9035-5009.pdf", "stored_filename": "4e7327c0-9134-43f0-8a38-d6642fb5b0fd.pdf", "file_path": "app/static/uploads/history/4e7327c0-9134-43f0-8a38-d6642fb5b0fd.pdf", "mime_type": "application/pdf", "file_size": 1105263, "upload_date": "2025-07-24 15:21:36"}]}, {"id": "4fe5c059-3d45-4c13-85a2-6299d68943c8", "equipment_id": "14206323", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Unit not functioning; flashing red indicator observed. \r\nSent to the Biomedical Department for evaluation. \r\nCurrently awaiting repair.", "created_at": "2025-07-24 15:28:39", "updated_at": "2025-07-24 15:28:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "400b37ef-473f-42e6-aefd-bed30a88820b", "note_id": "4fe5c059-3d45-4c13-85a2-6299d68943c8", "original_filename": "14206323.pdf", "stored_filename": "22f32718-ecdc-4628-9158-04ffd6970b27.pdf", "file_path": "app/static/uploads/history/22f32718-ecdc-4628-9158-04ffd6970b27.pdf", "mime_type": "application/pdf", "file_size": 1070863, "upload_date": "2025-07-24 15:28:39"}]}, {"id": "a4935356-98e7-43c7-ad91-7355d96b1e61", "equipment_id": "360713-M1600662004", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: ECG lead disconnection during procedure. Assessed and resolved by the Biomedical Department.", "created_at": "2025-07-24 15:34:25", "updated_at": "2025-07-24 15:34:25", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "cdd35235-a708-4f33-b6fe-aa20c60aeca7", "note_id": "a4935356-98e7-43c7-ad91-7355d96b1e61", "original_filename": "360713-M1600662004.pdf", "stored_filename": "174dd73c-7ea9-4fd8-ac0c-1671f8e00b95.pdf", "file_path": "app/static/uploads/history/174dd73c-7ea9-4fd8-ac0c-1671f8e00b95.pdf", "mime_type": "application/pdf", "file_size": 1016543, "upload_date": "2025-07-24 15:34:25"}]}, {"id": "c1468d77-85c3-4dfe-9268-bd6929274ab3", "equipment_id": "6120171-220UKN", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Reported not working.\r\nSent to the Biomedical Department for evaluation. \r\nCurrently awaiting repair.", "created_at": "2025-07-24 15:53:30", "updated_at": "2025-07-24 15:53:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "606d30e1-56d7-413c-91a4-c3bd7f63f5b4", "note_id": "c1468d77-85c3-4dfe-9268-bd6929274ab3", "original_filename": "LOG__2480.pdf", "stored_filename": "cd6894f5-881b-464f-9274-fc63575b05ea.pdf", "file_path": "app/static/uploads/history/cd6894f5-881b-464f-9274-fc63575b05ea.pdf", "mime_type": "application/pdf", "file_size": 985422, "upload_date": "2025-07-24 15:53:30"}]}, {"id": "68694aa7-405a-40ee-9f08-1cd0391e5547", "equipment_id": "91117001359", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Light suddenly turned off; ON switch button was found to be defective. \r\nIssue was repaired by the Biomedical Department. \r\nUnit is now fully operational.", "created_at": "2025-07-24 16:57:05", "updated_at": "2025-07-24 16:57:05", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "08c78fd4-3ec1-4399-8068-c12800343ec9", "note_id": "68694aa7-405a-40ee-9f08-1cd0391e5547", "original_filename": "LOG__1951.pdf", "stored_filename": "36ec4380-0b03-45e3-8739-9b1198fb4a5a.pdf", "file_path": "app/static/uploads/history/36ec4380-0b03-45e3-8739-9b1198fb4a5a.pdf", "mime_type": "application/pdf", "file_size": 1028015, "upload_date": "2025-07-24 16:57:05"}]}, {"id": "172c1e1f-e071-4e81-97dd-af83aa0e3a76", "equipment_id": "9914-9035-5119", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Machine suddenly shut down due to a power failure. Following the incident, the unit could not be calibrated. \r\nThe issue was assessed and resolved by the Biomedical Team. \r\nUnit is now fully functional.", "created_at": "2025-07-24 17:01:31", "updated_at": "2025-07-24 17:01:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "bf9b2ec7-fc84-42a5-9da2-260fd7092e2c", "note_id": "172c1e1f-e071-4e81-97dd-af83aa0e3a76", "original_filename": "9914-9035-5119.pdf", "stored_filename": "13be6a94-e6f0-4389-83ca-37952954e284.pdf", "file_path": "app/static/uploads/history/13be6a94-e6f0-4389-83ca-37952954e284.pdf", "mime_type": "application/pdf", "file_size": 1093647, "upload_date": "2025-07-24 17:01:31"}]}, {"id": "28101055-76a0-4207-9f70-69837dc4e0e0", "equipment_id": "027390-19770208", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Machine displayed an error message. \r\nUnit was sent to the Biomedical Department and is currently awaiting repair.", "created_at": "2025-07-24 17:05:17", "updated_at": "2025-07-24 17:05:17", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a4091d2c-6025-44c6-aa47-cef65c2a6044", "note_id": "28101055-76a0-4207-9f70-69837dc4e0e0", "original_filename": "027390-19770208.pdf", "stored_filename": "f3e0b575-7b04-4348-9918-fc00fed493c1.pdf", "file_path": "app/static/uploads/history/f3e0b575-7b04-4348-9918-fc00fed493c1.pdf", "mime_type": "application/pdf", "file_size": 1075711, "upload_date": "2025-07-24 17:05:17"}]}, {"id": "b25adb5a-a22a-46a0-a0ad-cb720928f1f2", "equipment_id": "19SDB000471", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Bed continues to move even after locking.\r\n Issue reported to the Biomedical Department. \r\nCurrently awaiting repair.", "created_at": "2025-07-24 17:08:15", "updated_at": "2025-07-24 17:08:15", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "93b953ed-e776-48f2-96a2-22e6e091d7cb", "note_id": "b25adb5a-a22a-46a0-a0ad-cb720928f1f2", "original_filename": "LOG__2334.pdf", "stored_filename": "deaa1d7a-0102-4025-b1ed-27df9dbea55c.pdf", "file_path": "app/static/uploads/history/deaa1d7a-0102-4025-b1ed-27df9dbea55c.pdf", "mime_type": "application/pdf", "file_size": 1001759, "upload_date": "2025-07-24 17:08:15"}]}, {"id": "75cde28b-c3f0-4ff1-a80a-70577454b837", "equipment_id": "19SDB000469", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Bed continues to move even after locking. \r\nIssue reported to the Biomedical Department. \r\nCurrently awaiting repair.", "created_at": "2025-07-24 17:08:56", "updated_at": "2025-07-24 17:08:56", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "408d7a26-eb67-47b6-b84d-6fd6f9b0716d", "note_id": "75cde28b-c3f0-4ff1-a80a-70577454b837", "original_filename": "LOG__2257.pdf", "stored_filename": "c9f41665-5a8f-4855-8900-95411f2d1533.pdf", "file_path": "app/static/uploads/history/c9f41665-5a8f-4855-8900-95411f2d1533.pdf", "mime_type": "application/pdf", "file_size": 1001759, "upload_date": "2025-07-24 17:08:56"}]}, {"id": "20d0ecc9-04f8-4989-9b29-86efd3d15748", "equipment_id": "19SDB000473", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Bed continues to move even after locking. \r\nIssue reported to the Biomedical Department. \r\nCurrently awaiting repair.", "created_at": "2025-07-24 17:09:31", "updated_at": "2025-07-24 17:09:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "88e55324-963c-4580-b594-69a8d8fa3391", "note_id": "20d0ecc9-04f8-4989-9b29-86efd3d15748", "original_filename": "LOG__2265.pdf", "stored_filename": "e0f13c13-37c5-4bb9-925d-6bdde2183f76.pdf", "file_path": "app/static/uploads/history/e0f13c13-37c5-4bb9-925d-6bdde2183f76.pdf", "mime_type": "application/pdf", "file_size": 1001759, "upload_date": "2025-07-24 17:09:31"}]}, {"id": "0fdb7704-757c-469b-aa60-d8d43e5b928d", "equipment_id": "05AF000643", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: The SPO2 machine probe was not functioning. \r\nThe issue was reported to the Biomedical Department, which provided a spare probe. \r\nThe machine is now operational.", "created_at": "2025-07-24 17:12:58", "updated_at": "2025-07-24 17:12:58", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "be95ab2b-b1b2-44c3-aa7e-1ebb06daa824", "note_id": "0fdb7704-757c-469b-aa60-d8d43e5b928d", "original_filename": "LOG_2310.pdf", "stored_filename": "e7f8eb43-faf7-4576-8abb-62247029b72c.pdf", "file_path": "app/static/uploads/history/e7f8eb43-faf7-4576-8abb-62247029b72c.pdf", "mime_type": "application/pdf", "file_size": 1039615, "upload_date": "2025-07-24 17:12:58"}]}, {"id": "adb8bb29-ac6e-48dc-9aa2-29a8db23ca77", "equipment_id": "EM03780319", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Additional channel required for TENS machine.\r\nAccessory was ordered and successfully provided.", "created_at": "2025-07-24 17:16:45", "updated_at": "2025-07-24 17:16:45", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "67193784-1d5e-44b9-a791-707376349abc", "note_id": "adb8bb29-ac6e-48dc-9aa2-29a8db23ca77", "original_filename": "LOG__1677.pdf", "stored_filename": "dbd6cc39-6bf6-4771-8933-e863633936a0.pdf", "file_path": "app/static/uploads/history/dbd6cc39-6bf6-4771-8933-e863633936a0.pdf", "mime_type": "application/pdf", "file_size": 1001759, "upload_date": "2025-07-24 17:16:45"}]}, {"id": "8b9d7d45-c170-446a-98ad-82acf79abba4", "equipment_id": "26221", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Battery cover missing.\r\nReferred to Biomedical and marked as obsolete.\r\nThe unit is old model and replacement part is difficult to obtain.", "created_at": "2025-07-26 06:47:09", "updated_at": "2025-07-26 06:47:09", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "a16a2872-ff23-4251-9f2f-e5e8b060fd9b", "note_id": "8b9d7d45-c170-446a-98ad-82acf79abba4", "original_filename": "26221.pdf", "stored_filename": "ac2d3005-d491-4602-a57c-63c235494287.pdf", "file_path": "app/static/uploads/history/ac2d3005-d491-4602-a57c-63c235494287.pdf", "mime_type": "application/pdf", "file_size": 997166, "upload_date": "2025-07-26 06:47:09"}]}, {"id": "57575690-27f3-4515-b337-5fa83d09bbbf", "equipment_id": "36410", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Suction machine not working. \r\nReferred to the Biomedical Department for repair.\r\nUnit remains unrepaired; a portable suction machine has been provided for temporary use.", "created_at": "2025-07-26 08:26:40", "updated_at": "2025-07-26 08:26:40", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "2efcac1c-4447-4fd1-b461-89dcb37267ca", "note_id": "57575690-27f3-4515-b337-5fa83d09bbbf", "original_filename": "36410-_d.pdf", "stored_filename": "1181c0ba-d70c-47c8-a2ce-d59bad64de82.pdf", "file_path": "app/static/uploads/history/1181c0ba-d70c-47c8-a2ce-d59bad64de82.pdf", "mime_type": "application/pdf", "file_size": 1024015, "upload_date": "2025-07-26 08:26:40"}]}, {"id": "e45d2744-ee69-49b6-ab5d-ffea408f95c8", "equipment_id": "35008190232", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "IR: Observed that the LED light was dimmer than usual, all connections were checked.\r\nIssue resolved and unit is functioning properly.", "created_at": "2025-07-28 06:29:39", "updated_at": "2025-07-28 06:29:40", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5a170732-af95-41b6-832f-2e332aea4dcb", "note_id": "e45d2744-ee69-49b6-ab5d-ffea408f95c8", "original_filename": "35008190232_compressed.pdf", "stored_filename": "3a48fc2f-cc92-4669-adf5-3ff467252cc8.pdf", "file_path": "app/static/uploads/history/3a48fc2f-cc92-4669-adf5-3ff467252cc8.pdf", "mime_type": "application/pdf", "file_size": 492898, "upload_date": "2025-07-28 06:29:40"}]}, {"id": "da812296-6c13-4fea-abb6-92f885358a45", "equipment_id": "19239", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Calibration error observed.\r\nReferred to Biomedical, ABG control (SCON) was replaced .\r\nIssue resolved and machine is now functioning properly.", "created_at": "2025-07-28 06:40:50", "updated_at": "2025-07-28 06:40:50", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "7db643b4-7b20-45f7-90a7-85bd726bb7d6", "note_id": "da812296-6c13-4fea-abb6-92f885358a45", "original_filename": "19239-_G.pdf", "stored_filename": "1eeb2c1a-14e3-4e0d-8ab5-af14283e325e.pdf", "file_path": "app/static/uploads/history/1eeb2c1a-14e3-4e0d-8ab5-af14283e325e.pdf", "mime_type": "application/pdf", "file_size": 481084, "upload_date": "2025-07-28 06:40:50"}]}, {"id": "e5cce037-9817-4416-93ca-927fd5efa09f", "equipment_id": "PH00350", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "July 8, 2024 Installed collimator unit configured. tested and confirmed all functions are working properly.", "created_at": "2025-07-29 15:34:49", "updated_at": "2025-07-29 15:34:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "45cf60b2-0518-491b-8dbb-ed5b26b56448", "note_id": "e5cce037-9817-4416-93ca-927fd5efa09f", "original_filename": "log_no._1773.pdf", "stored_filename": "7f5a9e39-d4e4-4ac2-98ef-bbed8cbf0b2b.pdf", "file_path": "app/static/uploads/history/7f5a9e39-d4e4-4ac2-98ef-bbed8cbf0b2b.pdf", "mime_type": "application/pdf", "file_size": 498944, "upload_date": "2025-07-29 15:34:49"}]}, {"id": "0ec48502-93e9-4820-ad90-b92273ab1cc8", "equipment_id": "PH00350", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "March 31, 2024 Checked the unit and found the collimator Y axis in the shutter motor is in a stuck condition. suspecting the motor's worn condition. Some adjustments were done, and it is working properly. after several restarts. If this happens again, the collimator module needs to be replaced.", "created_at": "2025-07-29 15:38:07", "updated_at": "2025-07-29 15:38:07", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3ebf810e-a4c2-4233-a23b-f34102997060", "note_id": "0ec48502-93e9-4820-ad90-b92273ab1cc8", "original_filename": "log_no._1773_1.pdf", "stored_filename": "538fb25f-8758-4af8-9e04-65b38c1e0f20.pdf", "file_path": "app/static/uploads/history/538fb25f-8758-4af8-9e04-65b38c1e0f20.pdf", "mime_type": "application/pdf", "file_size": 567952, "upload_date": "2025-07-29 15:38:07"}]}, {"id": "9e358972-dc99-4c46-8699-2437027692fa", "equipment_id": "PH00350", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "AUGUST 22, 2024 Unit checked, suspected issue with the logic control and exposure control boards. Manufacturer confirmed spare parts are unavailable.", "created_at": "2025-07-29 15:38:49", "updated_at": "2025-07-29 15:38:49", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "126969b2-25cf-430e-ae7f-41f988e66027", "note_id": "9e358972-dc99-4c46-8699-2437027692fa", "original_filename": "log_no._1773_2.pdf", "stored_filename": "581af93c-1046-4f92-bf20-95774600c38d.pdf", "file_path": "app/static/uploads/history/581af93c-1046-4f92-bf20-95774600c38d.pdf", "mime_type": "application/pdf", "file_size": 537344, "upload_date": "2025-07-29 15:38:49"}]}, {"id": "252b0752-fe03-4d81-a128-dc976f7e503c", "equipment_id": "11304615J", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "July 28, 2025. Found Thermostat tripped and reset the thermostat. Found unit is working properly.", "created_at": "2025-07-29 16:55:19", "updated_at": "2025-07-29 16:55:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "242fda91-7235-4f8f-ade0-ec7cd6f581bb", "note_id": "252b0752-fe03-4d81-a128-dc976f7e503c", "original_filename": "11304615J.pdf", "stored_filename": "c2a06a0f-068b-4dd1-ae13-159f982e918c.pdf", "file_path": "app/static/uploads/history/c2a06a0f-068b-4dd1-ae13-159f982e918c.pdf", "mime_type": "application/pdf", "file_size": 532576, "upload_date": "2025-07-29 16:55:19"}]}, {"id": "d5dbc76d-a2dd-496c-af76-22898d52f325", "equipment_id": "918056", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit sent to Biomedical Workshop for inspection and returned after assessment.", "created_at": "2025-07-29 20:17:40", "updated_at": "2025-07-29 20:17:40", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "90faf649-2de1-4b40-914f-56f92a90d5f4", "note_id": "d5dbc76d-a2dd-496c-af76-22898d52f325", "original_filename": "918054.pdf", "stored_filename": "7450acf7-7da0-4bbe-aaab-861c5e49dd21.pdf", "file_path": "app/static/uploads/history/7450acf7-7da0-4bbe-aaab-861c5e49dd21.pdf", "mime_type": "application/pdf", "file_size": 987870, "upload_date": "2025-07-29 20:17:40"}]}, {"id": "0def4516-aab8-4a09-964e-985dd8927d35", "equipment_id": "1050144", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Battery not charging. \r\nWill check availability of replacement battery. l\r\nUnit remains operational when connected to power cord", "created_at": "2025-07-29 20:44:13", "updated_at": "2025-07-29 20:44:13", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "04a0566a-0547-4358-8488-8782dc34ef8e", "note_id": "0def4516-aab8-4a09-964e-985dd8927d35", "original_filename": "1050144.pdf", "stored_filename": "c94e878c-92fc-44d5-a2f9-ff968b4a530e.pdf", "file_path": "app/static/uploads/history/c94e878c-92fc-44d5-a2f9-ff968b4a530e.pdf", "mime_type": "application/pdf", "file_size": 777678, "upload_date": "2025-07-29 20:44:13"}]}, {"id": "76c9636c-217e-4c1c-b565-880064596e73", "equipment_id": "2017123368", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found that the handpiece sensors were placed in the opposite direction. \r\nRepositioned them correctly. \r\nThe unit is now functioning properly.", "created_at": "2025-07-29 20:58:37", "updated_at": "2025-07-29 20:58:37", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "390a45e1-6db3-49ee-961f-fa2530f0105a", "note_id": "76c9636c-217e-4c1c-b565-880064596e73", "original_filename": "2017123368.pdf", "stored_filename": "96e48958-a33b-4aac-9192-0f69da4045d7.pdf", "file_path": "app/static/uploads/history/96e48958-a33b-4aac-9192-0f69da4045d7.pdf", "mime_type": "application/pdf", "file_size": 739278, "upload_date": "2025-07-29 20:58:37"}]}, {"id": "a8425e73-4039-4325-be71-089609712f43", "equipment_id": "4336", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced both the scaler sensor and broad sensor with new one.", "created_at": "2025-07-30 06:34:56", "updated_at": "2025-07-30 06:34:56", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ae6b700f-3df4-4ff5-9133-e653a291f1cd", "note_id": "a8425e73-4039-4325-be71-089609712f43", "original_filename": "4336.pdf", "stored_filename": "60a4bb14-0446-495a-bf6c-8c08760aae03.pdf", "file_path": "app/static/uploads/history/60a4bb14-0446-495a-bf6c-8c08760aae03.pdf", "mime_type": "application/pdf", "file_size": 566959, "upload_date": "2025-07-30 06:34:56"}]}, {"id": "7ceadd0a-91a7-4256-9cde-da79b51eacbd", "equipment_id": "20206122", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Identified a battery failure. \r\nThe battery was discharged, and the error was cleared. \r\nUnit functionality has been confirmed.", "created_at": "2025-07-30 07:00:17", "updated_at": "2025-07-30 07:00:17", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f2953fde-07c2-4742-a85b-1f22ca585697", "note_id": "7ceadd0a-91a7-4256-9cde-da79b51eacbd", "original_filename": "20206122.pdf", "stored_filename": "58634f29-35aa-4c22-8a79-7aff60778917.pdf", "file_path": "app/static/uploads/history/58634f29-35aa-4c22-8a79-7aff60778917.pdf", "mime_type": "application/pdf", "file_size": 511360, "upload_date": "2025-07-30 07:00:17"}]}, {"id": "aeaa385f-bd43-4841-9525-a7cdffef507c", "equipment_id": "72210006", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Some accessories of the unit have been taken to the company.", "created_at": "2025-07-30 07:39:26", "updated_at": "2025-07-30 07:39:26", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "481e8fb6-62c8-483b-874c-f35e9f8f03b4", "note_id": "aeaa385f-bd43-4841-9525-a7cdffef507c", "original_filename": "72210006-_d.pdf", "stored_filename": "bfdb6af2-dc32-43b1-8db5-9b81233b0e2e.pdf", "file_path": "app/static/uploads/history/bfdb6af2-dc32-43b1-8db5-9b81233b0e2e.pdf", "mime_type": "application/pdf", "file_size": 537888, "upload_date": "2025-07-30 07:39:26"}]}, {"id": "538a45ff-0938-4ed7-a25f-16c2963d6494", "equipment_id": "14100107", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "It was found that the unit requires a software update to support the new handpiece. \r\nAlso, calibration and laser alignment are needed to ensure proper functionality. \r\nA quotation for the required services will be sent as soon as possible.", "created_at": "2025-07-30 07:51:19", "updated_at": "2025-07-30 07:51:19", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "ab21dfa9-a050-4ff8-b626-ea4f2012c200", "note_id": "538a45ff-0938-4ed7-a25f-16c2963d6494", "original_filename": "14100107.pdf", "stored_filename": "55dfdf9f-aef1-4c5b-b7fe-3451d2ff1375.pdf", "file_path": "app/static/uploads/history/55dfdf9f-aef1-4c5b-b7fe-3451d2ff1375.pdf", "mime_type": "application/pdf", "file_size": 510528, "upload_date": "2025-07-30 07:51:19"}]}, {"id": "913d7808-5553-4b0c-8f15-12b9b97af430", "equipment_id": "9A25B8", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Checked the unit and validate the software license with the help of manufacturer through online support.\r\nNow the unit is working properly.", "created_at": "2025-07-30 08:10:36", "updated_at": "2025-07-30 08:10:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "8ce2f57d-6504-45fa-8823-8e721c3bff85", "note_id": "913d7808-5553-4b0c-8f15-12b9b97af430", "original_filename": "9A25B8.pdf", "stored_filename": "0be6a133-a861-435c-bd3e-3f9febdb0440.pdf", "file_path": "app/static/uploads/history/0be6a133-a861-435c-bd3e-3f9febdb0440.pdf", "mime_type": "application/pdf", "file_size": 440384, "upload_date": "2025-07-30 08:10:36"}]}, {"id": "f239e5e3-0ee6-4562-87f7-df76c215bff4", "equipment_id": "*********", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The lock was not functioning properly. \r\nIt has been repaired and tested, and is now confirmed to be working correctly.", "created_at": "2025-07-30 08:37:21", "updated_at": "2025-07-30 08:37:21", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d6dc3c8c-38c4-46b3-872c-d54efd5c0c52", "note_id": "f239e5e3-0ee6-4562-87f7-df76c215bff4", "original_filename": "*********.pdf", "stored_filename": "f2df404b-0f26-433e-9295-faa31a9a945a.pdf", "file_path": "app/static/uploads/history/f2df404b-0f26-433e-9295-faa31a9a945a.pdf", "mime_type": "application/pdf", "file_size": 506944, "upload_date": "2025-07-30 08:37:21"}]}, {"id": "19674d92-4db6-4607-a322-6161cd5f5458", "equipment_id": "951291", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The lamp inside the module was found to be faulty. \r\nA quotation for the repair is sent.", "created_at": "2025-07-30 08:45:36", "updated_at": "2025-07-30 08:45:36", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "99b126b5-7ecd-443f-b74c-d18d194324e7", "note_id": "19674d92-4db6-4607-a322-6161cd5f5458", "original_filename": "951291.pdf", "stored_filename": "f8f1d958-fb72-47f8-8afb-782e0f9b08a2.pdf", "file_path": "app/static/uploads/history/f8f1d958-fb72-47f8-8afb-782e0f9b08a2.pdf", "mime_type": "application/pdf", "file_size": 501088, "upload_date": "2025-07-30 08:45:36"}]}, {"id": "f5a0b80b-ce41-4725-aef6-8ac5900038d5", "equipment_id": "13X22079", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Inspected the unit and found leaks from both the door and the side. \r\nThe door was adjusted, and the chamber and filter were thoroughly cleaned. \r\nAfter running one cycle, the error and leaks persisted. \r\nReplacement of the unit is recommended.", "created_at": "2025-07-30 08:56:46", "updated_at": "2025-07-30 08:56:46", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "69f9b8a3-c732-4962-9380-c0988c0b134b", "note_id": "f5a0b80b-ce41-4725-aef6-8ac5900038d5", "original_filename": "13X22079.pdf", "stored_filename": "00397d7e-709a-49f6-8856-c4a11085004e.pdf", "file_path": "app/static/uploads/history/00397d7e-709a-49f6-8856-c4a11085004e.pdf", "mime_type": "application/pdf", "file_size": 505568, "upload_date": "2025-07-30 08:56:46"}]}, {"id": "7ce528d1-4e19-45ca-855a-a48130767f59", "equipment_id": "13X22079", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "The Bowie-Dick test performed in the morning did not pass.\r\n The unit was inspected, and all basic tests were conducted, with results within acceptable limits.\r\n The machine has been kept under observation for one day to monitor.", "created_at": "2025-07-30 08:59:42", "updated_at": "2025-07-30 08:59:42", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "50431426-bbea-4722-b58d-418fc99dc90c", "note_id": "7ce528d1-4e19-45ca-855a-a48130767f59", "original_filename": "13X22079-_b.pdf", "stored_filename": "d155fa7b-d55e-4616-b409-882c6c2b1f4d.pdf", "file_path": "app/static/uploads/history/d155fa7b-d55e-4616-b409-882c6c2b1f4d.pdf", "mime_type": "application/pdf", "file_size": 449280, "upload_date": "2025-07-30 08:59:42"}]}, {"id": "632e3178-94c1-4a61-8250-ad09e276fae0", "equipment_id": "13X22079", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "During the cycle, steam was observed leaking from the bottom part of the door. \r\nThe door was adjusted and realigned. \r\nAfter testing, a full cycle was successfully completed with no leaks detected. \r\nThe unit is now functioning properly.", "created_at": "2025-07-30 09:03:28", "updated_at": "2025-07-30 09:03:29", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "4d57cf80-28a3-4e66-b17c-1829126d3555", "note_id": "632e3178-94c1-4a61-8250-ad09e276fae0", "original_filename": "13X22079-_c.pdf", "stored_filename": "95726d5b-d09a-42a8-8809-ac414ba27327.pdf", "file_path": "app/static/uploads/history/95726d5b-d09a-42a8-8809-ac414ba27327.pdf", "mime_type": "application/pdf", "file_size": 450112, "upload_date": "2025-07-30 09:03:29"}]}, {"id": "691bcaf2-5754-4c2f-b750-5f12ff275f32", "equipment_id": "5627", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced faulty front panel. \r\nAll functions have been tested and are now working correctly.", "created_at": "2025-07-30 09:29:02", "updated_at": "2025-07-30 09:29:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "9b1ae5e2-d7a4-4b53-8536-f6ee20716eda", "note_id": "691bcaf2-5754-4c2f-b750-5f12ff275f32", "original_filename": "5627.pdf", "stored_filename": "e2ca0606-dbc6-4ba1-b0cb-a5bd67ead891.pdf", "file_path": "app/static/uploads/history/e2ca0606-dbc6-4ba1-b0cb-a5bd67ead891.pdf", "mime_type": "application/pdf", "file_size": 673781, "upload_date": "2025-07-30 09:29:02"}]}, {"id": "11b65621-a031-4bc5-9ca2-4a1d63832b8d", "equipment_id": "ME3005596", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Testing probe found to be faulty and requires replacement.", "created_at": "2025-07-30 09:49:00", "updated_at": "2025-07-30 09:49:00", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "1fc3b0bc-d0c4-4df3-982c-07dddc3a0f2e", "note_id": "11b65621-a031-4bc5-9ca2-4a1d63832b8d", "original_filename": "ME3005596.pdf", "stored_filename": "7fa34a3c-9845-403f-b237-824971fa7074.pdf", "file_path": "app/static/uploads/history/7fa34a3c-9845-403f-b237-824971fa7074.pdf", "mime_type": "application/pdf", "file_size": 647088, "upload_date": "2025-07-30 09:49:00"}]}, {"id": "827de17d-a393-4249-b49d-239c72302bd5", "equipment_id": "5627", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Screen found to be unresponsive. \r\nUnit has been taken for further evaluation, after which a repair quotation will be provided.\r\nBattery also requires replacement.", "created_at": "2025-07-30 09:52:22", "updated_at": "2025-07-30 09:52:22", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "f8859656-d81d-484d-9fb9-81fc0c621831", "note_id": "827de17d-a393-4249-b49d-239c72302bd5", "original_filename": "5627-_B.pdf", "stored_filename": "a24bb005-764e-4374-bcdd-53c207922628.pdf", "file_path": "app/static/uploads/history/a24bb005-764e-4374-bcdd-53c207922628.pdf", "mime_type": "application/pdf", "file_size": 565904, "upload_date": "2025-07-30 09:52:22"}]}, {"id": "423da39d-bb33-46cc-9e18-1638ef8e7f38", "equipment_id": "5627", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Replaced two batteries, updated software, and performed full-scale calibration. \r\nUnit is now functioning properly.", "created_at": "2025-07-30 09:54:51", "updated_at": "2025-07-30 09:54:51", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "354c9cf3-c132-4a91-915b-4dac588b25e6", "note_id": "423da39d-bb33-46cc-9e18-1638ef8e7f38", "original_filename": "5627-_C.pdf", "stored_filename": "3f782d33-5b55-4bdc-9154-95eb91ab9b42.pdf", "file_path": "app/static/uploads/history/3f782d33-5b55-4bdc-9154-95eb91ab9b42.pdf", "mime_type": "application/pdf", "file_size": 1013023, "upload_date": "2025-07-30 09:54:51"}]}, {"id": "33cfa60a-3686-483d-a1e9-5b06bd2c5d94", "equipment_id": "1151798", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Display is not working. \r\nFurther testing is needed to determine whether the issue is with the battery or the display. \r\nA new battery will be brought and test the unit.", "created_at": "2025-07-30 10:06:01", "updated_at": "2025-07-30 10:06:01", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "77964873-c885-48b1-8dde-980eef0c7880", "note_id": "33cfa60a-3686-483d-a1e9-5b06bd2c5d94", "original_filename": "1151798.pdf", "stored_filename": "de79e88e-f53e-482c-81c9-59ae8ff348ac.pdf", "file_path": "app/static/uploads/history/de79e88e-f53e-482c-81c9-59ae8ff348ac.pdf", "mime_type": "application/pdf", "file_size": 806974, "upload_date": "2025-07-30 10:06:01"}]}, {"id": "382fdfa6-e633-4fa3-96cc-3a0a5a4af67a", "equipment_id": "200720021935", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "NOTE: back-up battery not working.", "created_at": "2025-07-30 10:14:16", "updated_at": "2025-07-30 10:14:16", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "75b46341-b838-4d2d-9a24-e84f3aa2df07", "note_id": "382fdfa6-e633-4fa3-96cc-3a0a5a4af67a", "original_filename": "200720021935.pdf", "stored_filename": "3d422fa2-0feb-45d6-8f2a-e9511680137b.pdf", "file_path": "app/static/uploads/history/3d422fa2-0feb-45d6-8f2a-e9511680137b.pdf", "mime_type": "application/pdf", "file_size": 926062, "upload_date": "2025-07-30 10:14:16"}]}, {"id": "a59a4ca1-32ce-4863-ae87-1ecd479a4956", "equipment_id": "VSC200405", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Print function was not working due to a power jam. \r\nRequired parts have been installed, and the system is now operational.", "created_at": "2025-07-30 13:01:02", "updated_at": "2025-07-30 13:01:02", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "13335baa-e9fb-4678-905e-b6cc52e55b1e", "note_id": "a59a4ca1-32ce-4863-ae87-1ecd479a4956", "original_filename": "VSC200405.pdf", "stored_filename": "b1d2b981-115b-4299-a32b-12e51efa923f.pdf", "file_path": "app/static/uploads/history/b1d2b981-115b-4299-a32b-12e51efa923f.pdf", "mime_type": "application/pdf", "file_size": 410392, "upload_date": "2025-07-30 13:01:02"}]}, {"id": "e0ebbaf0-2dde-489b-aef1-4bac806e2a36", "equipment_id": "200720021630", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "No battery backup found; battery pack requires replacement. \r\nPerformed hardware, front panel, and paddle interface tests. \r\nAll components tested and found to be functioning properly.", "created_at": "2025-07-30 13:09:38", "updated_at": "2025-07-30 13:09:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d8490d2a-20c9-47cc-94f6-41b049abc3ae", "note_id": "e0ebbaf0-2dde-489b-aef1-4bac806e2a36", "original_filename": "200720021630.pdf", "stored_filename": "547d5d0a-6c6a-46d5-8091-64daf56a2215.pdf", "file_path": "app/static/uploads/history/547d5d0a-6c6a-46d5-8091-64daf56a2215.pdf", "mime_type": "application/pdf", "file_size": 578016, "upload_date": "2025-07-30 13:09:39"}]}, {"id": "e1344210-41d5-4358-85bd-45dbbe35afb7", "equipment_id": "27192", "equipment_type": "ocm", "author_id": "admin", "author_name": "admin", "note_text": "Found one screw missing from the base cover and the main rod connection loose. \r\nRe-secured the main rod and base cover. \r\nChecked and confirmed the unit is functioning properly.", "created_at": "2025-07-30 13:16:20", "updated_at": "2025-07-30 13:16:20", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "3c886247-7269-4808-a788-3d8d4b805bee", "note_id": "e1344210-41d5-4358-85bd-45dbbe35afb7", "original_filename": "27192-_B.pdf", "stored_filename": "d1f370e9-775a-454f-8e15-c50bac79d578.pdf", "file_path": "app/static/uploads/history/d1f370e9-775a-454f-8e15-c50bac79d578.pdf", "mime_type": "application/pdf", "file_size": 455968, "upload_date": "2025-07-30 13:16:20"}]}, {"id": "21c04b82-00a7-4a7e-8d6d-81bea3ca09d9", "equipment_id": "200720021612", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "No battery backup found; battery pack requires replacement. \r\nPerformed system tests, unit is functioning well.", "created_at": "2025-07-30 13:32:03", "updated_at": "2025-07-30 13:32:03", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fc79ac36-592d-45d5-925a-f0c8a7b6cad7", "note_id": "21c04b82-00a7-4a7e-8d6d-81bea3ca09d9", "original_filename": "200720021612.pdf", "stored_filename": "993bcf34-48f8-4f2a-87eb-2174a532b3d3.pdf", "file_path": "app/static/uploads/history/993bcf34-48f8-4f2a-87eb-2174a532b3d3.pdf", "mime_type": "application/pdf", "file_size": 591136, "upload_date": "2025-07-30 13:32:03"}]}, {"id": "ab203f15-bb82-416a-a0c4-8cd6c1d2f374", "equipment_id": "M1521040056", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Unit is not powering up. \r\nThe unit is being taken to our workshop for inspection. \r\nWe will update you as soon as the assessment is completed.", "created_at": "2025-07-30 13:42:07", "updated_at": "2025-07-30 13:42:07", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "5d6c1865-e1a6-46ca-846f-61028669c82a", "note_id": "ab203f15-bb82-416a-a0c4-8cd6c1d2f374", "original_filename": "LOG__2357.pdf", "stored_filename": "91e4a27d-7c20-4dfc-99b5-608c559a5adf.pdf", "file_path": "app/static/uploads/history/91e4a27d-7c20-4dfc-99b5-608c559a5adf.pdf", "mime_type": "application/pdf", "file_size": 525584, "upload_date": "2025-07-30 13:42:07"}]}, {"id": "85bc9564-343d-4705-8731-1307aed474d0", "equipment_id": "AMXG00825", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Checked unit and replaced BTV microswitch. \r\nPerformed tests, unit is operational.", "created_at": "2025-07-30 13:51:30", "updated_at": "2025-07-30 13:51:30", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "57f7eea3-1b5d-44e0-a0f9-f3d58fe2c5f1", "note_id": "85bc9564-343d-4705-8731-1307aed474d0", "original_filename": "AMXG00825.pdf", "stored_filename": "41915ae8-406a-4fa7-94a0-7802121398a6.pdf", "file_path": "app/static/uploads/history/41915ae8-406a-4fa7-94a0-7802121398a6.pdf", "mime_type": "application/pdf", "file_size": 649824, "upload_date": "2025-07-30 13:51:30"}]}, {"id": "d6363bba-293e-4185-8266-9c6e7781f998", "equipment_id": "200720021612", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Performed hardware and keypad tests. \r\nAll components are in good working condition.", "created_at": "2025-07-30 14:01:27", "updated_at": "2025-07-30 14:01:27", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d87a6b58-a142-4aa8-86d7-3f427c968e51", "note_id": "d6363bba-293e-4185-8266-9c6e7781f998", "original_filename": "200720021612-_B.pdf", "stored_filename": "e340570a-fd7b-420d-bab7-0785fdb70a27.pdf", "file_path": "app/static/uploads/history/e340570a-fd7b-420d-bab7-0785fdb70a27.pdf", "mime_type": "application/pdf", "file_size": 523504, "upload_date": "2025-07-30 14:01:27"}]}, {"id": "b01b5869-6152-4a74-9fd5-0a2ba6cdb83e", "equipment_id": "200720021935", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Battery found to be faulty and requires replacement.", "created_at": "2025-07-30 14:04:39", "updated_at": "2025-07-30 14:04:39", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "fede6457-8d80-407a-a4db-4666a892360b", "note_id": "b01b5869-6152-4a74-9fd5-0a2ba6cdb83e", "original_filename": "200720021935-_B.pdf", "stored_filename": "a6a13ab1-a183-4589-a1ca-dbed58dc13bb.pdf", "file_path": "app/static/uploads/history/a6a13ab1-a183-4589-a1ca-dbed58dc13bb.pdf", "mime_type": "application/pdf", "file_size": 523296, "upload_date": "2025-07-30 14:04:39"}]}, {"id": "9b4e86ab-c358-493f-9b5f-7d5678479dd3", "equipment_id": "200720021612", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Battery found to be faulty and requires replacement.", "created_at": "2025-07-30 14:07:31", "updated_at": "2025-07-30 14:07:31", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "cc4bcd29-c1d9-4f79-a7ac-6c0ce44e1503", "note_id": "9b4e86ab-c358-493f-9b5f-7d5678479dd3", "original_filename": "200720021612-_C.pdf", "stored_filename": "c59a4d86-ed53-400e-b4c4-dc4cece6a271.pdf", "file_path": "app/static/uploads/history/c59a4d86-ed53-400e-b4c4-dc4cece6a271.pdf", "mime_type": "application/pdf", "file_size": 523296, "upload_date": "2025-07-30 14:07:31"}]}, {"id": "aef6c4b8-1147-4bc3-89c8-c57c4f516814", "equipment_id": "5231035", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "As per the manufacturer's response, the heating pad cable has physical damage.\r\nThe heating pad needs to be returned to the supplier for inspection and repair.", "created_at": "2025-07-30 14:23:05", "updated_at": "2025-07-30 14:23:05", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "24e45eb2-3db5-4c58-baa0-ea60858a5d0e", "note_id": "aef6c4b8-1147-4bc3-89c8-c57c4f516814", "original_filename": "5231035.pdf", "stored_filename": "5db37719-8dce-4d67-9761-c9f34b525194.pdf", "file_path": "app/static/uploads/history/5db37719-8dce-4d67-9761-c9f34b525194.pdf", "mime_type": "application/pdf", "file_size": 756430, "upload_date": "2025-07-30 14:23:05"}]}, {"id": "c72b2db6-57e2-4944-be80-c9189b842ed5", "equipment_id": "5231035", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "Found physical damage on the heating pad connector. \r\nAs per the manufacturer, the damaged pad will be replaced with a new one after inspection. The unit is currently operating with a new heating pad.", "created_at": "2025-07-30 14:28:51", "updated_at": "2025-07-30 14:28:51", "last_modified_by": null, "last_modified_by_name": null, "is_edited": false, "attachments": [{"id": "d9a7045c-eb2c-4ea8-81bf-3b3069aafa8b", "note_id": "c72b2db6-57e2-4944-be80-c9189b842ed5", "original_filename": "5231035-_B.pdf", "stored_filename": "c7491ee1-4003-4245-a9a0-24f8f552218c.pdf", "file_path": "app/static/uploads/history/c7491ee1-4003-4245-a9a0-24f8f552218c.pdf", "mime_type": "application/pdf", "file_size": 528944, "upload_date": "2025-07-30 14:28:51"}]}, {"id": "e152595a-2f82-4046-8877-bc1b542f5302", "equipment_id": "601900026", "equipment_type": "ppm", "author_id": "admin", "author_name": "admin", "note_text": "note: Centrifuge lid found to be defective. \r\nSwitch needs replacement. \r\nAwaiting arrival of spare parts.", "created_at": "2025-07-30 20:02:12", "updated_at": "2025-07-31 01:18:55", "last_modified_by": "admin", "last_modified_by_name": "admin", "is_edited": true, "attachments": [{"id": "97ee2803-4ad4-44b2-9239-5abd29ababd7", "note_id": "e152595a-2f82-4046-8877-bc1b542f5302", "original_filename": "601900026.pdf", "stored_filename": "Unknown-Machine-601900026-2025-07-30-011855.pdf", "file_path": "app/static/uploads/history\\Unknown-Machine-601900026-2025-07-30-011855.pdf", "mime_type": "application/pdf", "file_size": 1099276, "upload_date": "2025-07-31 01:18:55"}]}]